import pystray
import tkinter as tk
from PIL import Image, ImageDraw
import json
import threading
import time
import os
from typing import Dict, Any, Optional
from api_client import ApiClient
from ui.balance_window import BalanceWindow


class BalanceTrayApp:
    """余额监控托盘应用"""
    
    def __init__(self):
        self.config = self._load_config()
        self.api_client = ApiClient()
        
        # 在主线程中创建tkinter根窗口
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏根窗口
        
        self.balance_window = BalanceWindow(self.config, self.root)
        self.icon = None
        self.data_cache = {"users": [], "pools": []}
        self.cache_file = os.path.join(os.path.dirname(__file__), "cache.json")  # 缓存文件路径
        self.is_refreshing = False
        self.refresh_thread = None
        self.running = True
        
        # 由于界面简化，不需要设置刷新回调
        # self.balance_window.set_refresh_callback(self.manual_refresh)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(os.path.dirname(__file__), "config.json")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _load_cache(self) -> Dict[str, Any]:
        """加载缓存数据"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    print(f"加载缓存数据 - 用户:{len(cache_data.get('users', []))}, 池:{len(cache_data.get('pools', []))}")
                    return cache_data
        except Exception as e:
            print(f"加载缓存失败: {e}")
        return {"users": [], "pools": []}
    
    def _save_cache(self, data: Dict[str, Any]):
        """保存数据到缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """默认配置"""
        return {
            "refresh_interval": 30,
            "window_width": 250,
            "window_height": 350,
            "users": [],
            "pools": [],
            "theme": {
                "bg_color": "#0b1220",
                "fg_color": "#e6edf3",
                "success_color": "#4ea1ff",
                "error_color": "#ff6b6b"
            }
        }
    
    def _create_icon_image(self) -> Image.Image:
        """创建托盘图标"""
        # 创建32x32的图标
        width = 32
        height = 32
        image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # 绘制圆形背景
        draw.ellipse([2, 2, width-2, height-2], fill=(78, 161, 255, 255), outline=(255, 255, 255, 255))
        
        # 绘制美元符号
        draw.text((10, 6), '$', fill=(255, 255, 255, 255), anchor='mm')
        
        return image
    
    def create_tray_icon(self):
        """创建系统托盘图标"""
        image = self._create_icon_image()
        
        menu = pystray.Menu(
            pystray.MenuItem("💰 查看余额", self.show_balance_window, default=True),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("⚙️ 打开配置", self.open_config),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("❌ 退出", self.quit_app)
        )
        
        self.icon = pystray.Icon(
            "balance_monitor",
            image,
            "余额监控",
            menu
        )
        
        # 设置鼠标悬停提示
        self.update_tooltip()
        
        return self.icon
    
    def show_balance_window(self, icon=None, item=None):
        """显示余额窗口"""
        try:
            # 使用show_window方法而不是create_window
            self.balance_window.show_window()
        except Exception as e:
            print(f"显示窗口失败: {e}")
    
    def manual_refresh(self, icon=None, item=None):
        """手动刷新"""
        if not self.is_refreshing:
            threading.Thread(target=self._refresh_data, daemon=True).start()
    
    def _refresh_data(self):
        """刷新数据（后台线程）"""
        if self.is_refreshing:
            return
            
        self.is_refreshing = True
        
        try:
            new_data = {"users": [], "pools": []}
            
            # 查询用户账户
            for user_config in self.config.get('users', []):
                if not user_config.get('enabled', True):
                    continue
                    
                user_data = {
                    "name": user_config.get('name', '未知用户'),
                    "key_masked": self.api_client.mask_key(user_config.get('key', ''))
                }
                
                # 添加手动配置的总额度
                if 'manual_total' in user_config:
                    user_data['manual_total'] = user_config['manual_total']
                
                try:
                    result = self.api_client.detect_and_fetch(
                        user_config['key'],
                        user_config.get('provider', 'auto')
                    )
                    user_data.update(result)
                except Exception as e:
                    user_data["error"] = str(e)
                
                new_data["users"].append(user_data)
            
            # 查询账号池
            for pool_config in self.config.get('pools', []):
                if not pool_config.get('enabled', True):
                    continue
                    
                pool_data = {
                    "name": pool_config.get('name', '未知池'),
                    "key_masked": self.api_client.mask_key(pool_config.get('key', ''))
                }
                
                # 添加手动配置的总额度
                if 'manual_total' in pool_config:
                    pool_data['manual_total'] = pool_config['manual_total']
                
                try:
                    result = self.api_client.detect_and_fetch(
                        pool_config['key'],
                        pool_config.get('provider', 'auto')
                    )
                    pool_data.update(result)
                except Exception as e:
                    pool_data["error"] = str(e)
                
                new_data["pools"].append(pool_data)
            
            # 更新缓存
            self.data_cache = new_data
            
            # 保存到缓存文件
            self._save_cache(new_data)
            
            # 线程安全地更新窗口显示
            self.balance_window.update_data(new_data)
            
            # 更新托盘提示
            self.update_tooltip()
            
            print(f"数据刷新完成 - 用户:{len(new_data['users'])}, 池:{len(new_data['pools'])}")
            
        except Exception as e:
            print(f"刷新数据失败: {e}")
        finally:
            self.is_refreshing = False
            # 界面简化，不需要通知窗口刷新完成
            # self.balance_window.on_refresh_complete()
    
    def update_tooltip(self):
        """更新托盘提示信息"""
        if not self.icon:
            return
            
        try:
            total = 0.0
            user_count = 0
            pool_count = 0
            error_count = 0
            
            # 计算统计信息
            for user in self.data_cache.get('users', []):
                user_count += 1
                if 'error' in user:
                    error_count += 1
                elif user.get('unlimited'):
                    pass  # 无限制不计入总额
                elif 'balance' in user and isinstance(user['balance'], (int, float)):
                    total += user['balance']
                elif 'daily' in user:
                    total += user['daily'].get('remain', 0)
            
            for pool in self.data_cache.get('pools', []):
                pool_count += 1
                if 'error' in pool:
                    error_count += 1
                elif pool.get('unlimited'):
                    pass
                elif 'balance' in pool and isinstance(pool['balance'], (int, float)):
                    total += pool['balance']
                elif 'daily' in pool:
                    total += pool['daily'].get('remain', 0)
            
            # 构建提示文本
            tooltip = f"余额监控 - 总计: ${total:.2f}\\n"
            tooltip += f"用户: {user_count}, 池: {pool_count}"
            if error_count > 0:
                tooltip += f", 错误: {error_count}"
            
            self.icon.title = tooltip
            
        except Exception as e:
            self.icon.title = f"余额监控 - 错误: {str(e)}"
    
    def open_config(self, icon=None, item=None):
        """打开配置文件"""
        config_path = os.path.join(os.path.dirname(__file__), "config.json")
        try:
            os.startfile(config_path)  # Windows
        except:
            try:
                os.system(f"open {config_path}")  # macOS
            except:
                try:
                    os.system(f"xdg-open {config_path}")  # Linux
                except:
                    print(f"请手动编辑配置文件: {config_path}")
    
    def quit_app(self, icon=None, item=None):
        """退出应用"""
        self.running = False
        if self.icon:
            self.icon.stop()
        # 关闭tkinter根窗口
        if self.root:
            self.root.quit()
            self.root.destroy()
    
    def start_background_refresh(self):
        """启动后台刷新线程"""
        def refresh_loop():
            while self.running:
                try:
                    self._refresh_data()
                    # 等待下次刷新
                    for _ in range(self.config.get('refresh_interval', 30)):
                        if not self.running:
                            break
                        time.sleep(1)
                except Exception as e:
                    print(f"后台刷新出错: {e}")
                    # 出错时等待较短时间再重试
                    for _ in range(10):
                        if not self.running:
                            break
                        time.sleep(1)
        
        self.refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
        self.refresh_thread.start()
    
    def run(self):
        """启动应用"""
        print("启动余额监控托盘应用...")
        
        # 先加载缓存数据（如果有）
        cached_data = self._load_cache()
        if cached_data.get('users') or cached_data.get('pools'):
            self.data_cache = cached_data
            self.balance_window.update_data(cached_data)
            print("缓存数据加载完成")
        
        # 创建托盘图标
        icon = self.create_tray_icon()
        
        # 启动后台刷新
        self.start_background_refresh()
        
        # 立即执行一次数据刷新（阻塞方式，确保首次启动就有数据）
        print("正在获取最新数据...")
        self._refresh_data()
        
        print("托盘应用已启动，右键查看菜单")
        
        # 在后台线程运行托盘图标
        def run_tray():
            icon.run()
        
        tray_thread = threading.Thread(target=run_tray, daemon=True)
        tray_thread.start()
        
        # 启动tkinter主循环
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\\n用户中断，退出应用")
            self.quit_app()


def main():
    app = BalanceTrayApp()
    app.run()


if __name__ == "__main__":
    main()