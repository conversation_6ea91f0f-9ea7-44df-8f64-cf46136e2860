{"未登录或登录已过期，请重新登录": "未登录或登录已过期，请重新登录", "登 录": "登 录", "使用 微信 继续": "使用 微信 继续", "使用 GitHub 继续": "使用 GitHub 继续", "使用 LinuxDO 继续": "使用 LinuxDO 继续", "使用 邮箱或用户名 登录": "使用 邮箱或用户名 登录", "没有账户？": "没有账户？", "用户名或邮箱": "用户名或邮箱", "请输入您的用户名或邮箱地址": "请输入您的用户名或邮箱地址", "请输入您的密码": "请输入您的密码", "继续": "继续", "忘记密码？": "忘记密码？", "其他登录选项": "其他登录选项", "微信扫码登录": "微信扫码登录", "登录": "登录", "微信扫码关注公众号，输入「验证码」获取验证码（三分钟内有效）": "微信扫码关注公众号，输入「验证码」获取验证码（三分钟内有效）", "验证码": "验证码", "处理中...": "处理中...", "绑定成功！": "绑定成功！", "登录成功！": "登录成功！", "操作失败，重定向至登录界面中...": "操作失败，重定向至登录界面中...", "出现错误，第 ${count} 次重试中...": "出现错误，第 ${count} 次重试中...", "无效的重置链接，请重新发起密码重置请求": "无效的重置链接，请重新发起密码重置请求", "密码已重置并已复制到剪贴板：": "密码已重置并已复制到剪贴板：", "密码重置确认": "密码重置确认", "等待获取邮箱信息...": "等待获取邮箱信息...", "新密码": "新密码", "密码已复制到剪贴板：": "密码已复制到剪贴板：", "密码重置完成": "密码重置完成", "确认重置密码": "确认重置密码", "返回登录": "返回登录", "请输入邮箱地址": "请输入邮箱地址", "请稍后几秒重试，Turnstile 正在检查用户环境！": "请稍后几秒重试，Turnstile 正在检查用户环境！", "重置邮件发送成功，请检查邮箱！": "重置邮件发送成功，请检查邮箱！", "密码重置": "密码重置", "请输入您的邮箱地址": "请输入您的邮箱地址", "重试": "重试", "想起来了？": "想起来了？", "注 册": "注 册", "使用 用户名 注册": "使用 用户名 注册", "已有账户？": "已有账户？", "用户名": "用户名", "请输入用户名": "请输入用户名", "输入密码，最短 8 位，最长 20 位": "输入密码，最短 8 位，最长 20 位", "确认密码": "确认密码", "输入邮箱地址": "输入邮箱地址", "获取验证码": "获取验证码", "输入验证码": "输入验证码", "或": "或", "其他注册选项": "其他注册选项", "加载中...": "加载中...", "复制代码": "复制代码", "代码已复制到剪贴板": "代码已复制到剪贴板", "复制失败，请手动复制": "复制失败，请手动复制", "显示更多": "显示更多", "关于我们": "关于我们", "关于项目": "关于项目", "联系我们": "联系我们", "功能特性": "功能特性", "快速开始": "快速开始", "安装指南": "安装指南", "API 文档": "API 文档", "基于New API的项目": "基于New API的项目", "版权所有": "版权所有", "设计与开发由": "设计与开发由", "首页": "首页", "控制台": "控制台", "文档": "文档", "关于": "关于", "注销成功!": "注销成功!", "个人设置": "个人设置", "API令牌": "API令牌", "退出": "退出", "关闭侧边栏": "关闭侧边栏", "打开侧边栏": "打开侧边栏", "关闭菜单": "关闭菜单", "打开菜单": "打开菜单", "演示站点": "演示站点", "自用模式": "自用模式", "系统公告": "系统公告", "切换主题": "切换主题", "切换语言": "切换语言", "暂无公告": "暂无公告", "暂无系统公告": "暂无系统公告", "今日关闭": "今日关闭", "关闭公告": "关闭公告", "数据看板": "数据看板", "绘图日志": "绘图日志", "任务日志": "任务日志", "渠道": "渠道", "兑换码": "兑换码", "用户管理": "用户管理", "操练场": "操练场", "聊天": "聊天", "管理员": "管理员", "个人中心": "个人中心", "展开侧边栏": "展开侧边栏", "AI 对话": "AI 对话", "选择模型开始对话": "选择模型开始对话", "显示调试": "显示调试", "请输入您的问题...": "请输入您的问题...", "已复制到剪贴板": "已复制到剪贴板", "复制失败": "复制失败", "正在构造请求体预览...": "正在构造请求体预览...", "暂无请求数据": "暂无请求数据", "暂无响应数据": "暂无响应数据", "内容较大，已启用性能优化模式": "内容较大，已启用性能优化模式", "内容较大，部分功能可能受限": "内容较大，部分功能可能受限", "已复制": "已复制", "正在处理大内容...": "正在处理大内容...", "显示完整内容": "显示完整内容", "收起": "收起", "配置已导出到下载文件夹": "配置已导出到下载文件夹", "导出配置失败: ": "导出配置失败: ", "确认导入配置": "确认导入配置", "导入的配置将覆盖当前设置，是否继续？": "导入的配置将覆盖当前设置，是否继续？", "取消": "取消", "配置导入成功": "配置导入成功", "导入配置失败: ": "导入配置失败: ", "重置配置": "重置配置", "将清除所有保存的配置并恢复默认设置，此操作不可撤销。是否继续？": "将清除所有保存的配置并恢复默认设置，此操作不可撤销。是否继续？", "重置选项": "重置选项", "是否同时重置对话消息？选择\"是\"将清空所有对话记录并恢复默认示例；选择\"否\"将保留当前对话记录。": "是否同时重置对话消息？选择\"是\"将清空所有对话记录并恢复默认示例；选择\"否\"将保留当前对话记录。", "同时重置消息": "同时重置消息", "仅重置配置": "仅重置配置", "配置和消息已全部重置": "配置和消息已全部重置", "配置已重置，对话消息已保留": "配置已重置，对话消息已保留", "已有保存的配置": "已有保存的配置", "暂无保存的配置": "暂无保存的配置", "导出配置": "导出配置", "导入配置": "导入配置", "导出": "导出", "导入": "导入", "调试信息": "调试信息", "预览请求体": "预览请求体", "实际请求体": "实际请求体", "预览更新": "预览更新", "最后请求": "最后请求", "操作暂时被禁用": "操作暂时被禁用", "复制": "复制", "编辑": "编辑", "切换为System角色": "切换为System角色", "切换为Assistant角色": "切换为Assistant角色", "删除": "删除", "请求发生错误": "请求发生错误", "系统消息": "系统消息", "请输入消息内容...": "请输入消息内容...", "保存": "保存", "模型配置": "模型配置", "分组": "分组", "请选择分组": "请选择分组", "请选择模型": "请选择模型", "思考中...": "思考中...", "思考过程": "思考过程", "选择同步渠道": "选择同步渠道", "搜索渠道名称或地址": "搜索渠道名称或地址", "暂无渠道": "暂无渠道", "暂无选择": "暂无选择", "无搜索结果": "无搜索结果", "公告已更新": "公告已更新", "公告更新失败": "公告更新失败", "系统名称已更新": "系统名称已更新", "系统名称更新失败": "系统名称更新失败", "系统信息": "系统信息", "当前版本": "当前版本", "检查更新": "检查更新", "启动时间": "启动时间", "通用设置": "通用设置", "设置公告": "设置公告", "个性化设置": "个性化设置", "系统名称": "系统名称", "在此输入系统名称": "在此输入系统名称", "设置系统名称": "设置系统名称", "Logo 图片地址": "Logo 图片地址", "在此输入 Logo 图片地址": "在此输入 Logo 图片地址", "首页内容": "首页内容", "设置首页内容": "设置首页内容", "设置关于": "设置关于", "页脚": "页脚", "设置页脚": "设置页脚", "详情": "详情", "刷新失败": "刷新失败", "令牌已重置并已复制到剪贴板": "令牌已重置并已复制到剪贴板", "加载模型列表失败": "加载模型列表失败", "系统令牌已复制到剪切板": "系统令牌已复制到剪切板", "请输入你的账户名以确认删除！": "请输入你的账户名以确认删除！", "账户已删除！": "账户已删除！", "微信账户绑定成功！": "微信账户绑定成功！", "请输入原密码！": "请输入原密码！", "请输入新密码！": "请输入新密码！", "新密码需要和原密码不一致！": "新密码需要和原密码不一致！", "两次输入的密码不一致！": "两次输入的密码不一致！", "密码修改成功！": "密码修改成功！", "验证码发送成功，请检查邮箱！": "验证码发送成功，请检查邮箱！", "请输入邮箱验证码！": "请输入邮箱验证码！", "邮箱账户绑定成功！": "邮箱账户绑定成功！", "无法复制到剪贴板，请手动复制": "无法复制到剪贴板，请手动复制", "设置保存成功": "设置保存成功", "设置保存失败": "设置保存失败", "超级管理员": "超级管理员", "普通用户": "普通用户", "当前余额": "当前余额", "历史消耗": "历史消耗", "请求次数": "请求次数", "默认": "默认", "可用模型": "可用模型", "模型列表": "模型列表", "点击模型名称可复制": "点击模型名称可复制", "没有可用模型": "没有可用模型", "该分类下没有可用模型": "该分类下没有可用模型", "更多": "更多", "个模型": "个模型", "账户绑定": "账户绑定", "未绑定": "未绑定", "修改绑定": "修改绑定", "微信": "微信", "已绑定": "已绑定", "未启用": "未启用", "绑定": "绑定", "安全设置": "安全设置", "系统访问令牌": "系统访问令牌", "用于API调用的身份验证令牌，请妥善保管": "用于API调用的身份验证令牌，请妥善保管", "生成令牌": "生成令牌", "密码管理": "密码管理", "定期更改密码可以提高账户安全性": "定期更改密码可以提高账户安全性", "修改密码": "修改密码", "此操作不可逆，所有数据将被永久删除": "此操作不可逆，所有数据将被永久删除", "删除账户": "删除账户", "其他设置": "其他设置", "通知设置": "通知设置", "邮件通知": "邮件通知", "通过邮件接收通知": "通过邮件接收通知", "Webhook通知": "Webhook通知", "通过HTTP请求接收通知": "通过HTTP请求接收通知", "请输入Webhook地址，例如: https://example.com/webhook": "请输入Webhook地址，例如: https://example.com/webhook", "只支持https，系统将以 POST 方式发送通知，请确保地址可以接收 POST 请求": "只支持https，系统将以 POST 方式发送通知，请确保地址可以接收 POST 请求", "接口凭证（可选）": "接口凭证（可选）", "请输入密钥": "请输入密钥", "密钥将以 Bearer 方式添加到请求头中，用于验证webhook请求的合法性": "密钥将以 Bearer 方式添加到请求头中，用于验证webhook请求的合法性", "通知邮箱": "通知邮箱", "留空则使用账号绑定的邮箱": "留空则使用账号绑定的邮箱", "设置用于接收额度预警的邮箱地址，不填则使用账号绑定的邮箱": "设置用于接收额度预警的邮箱地址，不填则使用账号绑定的邮箱", "额度预警阈值": "额度预警阈值", "请输入预警额度": "请输入预警额度", "当剩余额度低于此数值时，系统将通过选择的方式发送通知": "当剩余额度低于此数值时，系统将通过选择的方式发送通知", "接受未设置价格模型": "接受未设置价格模型", "当模型没有设置价格时仍接受调用，仅当您信任该网站时使用，可能会产生高额费用": "当模型没有设置价格时仍接受调用，仅当您信任该网站时使用，可能会产生高额费用", "IP记录": "IP记录", "记录请求与错误日志 IP": "记录请求与错误日志 IP", "开启后，仅“消费”和“错误”日志将记录您的客户端 IP 地址": "开启后，仅“消费”和“错误”日志将记录您的客户端 IP 地址", "绑定邮箱地址": "绑定邮箱地址", "重新发送": "重新发送", "绑定微信账户": "绑定微信账户", "删除账户确认": "删除账户确认", "您正在删除自己的帐户，将清空所有数据且不可恢复": "您正在删除自己的帐户，将清空所有数据且不可恢复", "请输入您的用户名以确认删除": "请输入您的用户名以确认删除", "输入你的账户名{{username}}以确认删除": "输入你的账户名{{username}}以确认删除", "原密码": "原密码", "请输入原密码": "请输入原密码", "请输入新密码": "请输入新密码", "确认新密码": "确认新密码", "请再次输入新密码": "请再次输入新密码", "模型倍率设置": "模型倍率设置", "可视化倍率设置": "可视化倍率设置", "未设置倍率模型": "未设置倍率模型", "上游倍率同步": "上游倍率同步", "未知类型": "未知类型", "标签聚合": "标签聚合", "已启用": "已启用", "自动禁用": "自动禁用", "未知状态": "未知状态", "未测试": "未测试", "名称": "名称", "类型": "类型", "状态": "状态", "，时间：": "，时间：", "响应时间": "响应时间", "已用/剩余": "已用/剩余", "剩余额度$": "剩余额度$", "，点击更新": "，点击更新", "已用额度": "已用额度", "修改子渠道优先级": "修改子渠道优先级", "确定要修改所有子渠道优先级为 ": "确定要修改所有子渠道优先级为 ", "权重": "权重", "修改子渠道权重": "修改子渠道权重", "确定要修改所有子渠道权重为 ": "确定要修改所有子渠道权重为 ", "确定是否要删除此渠道？": "确定是否要删除此渠道？", "此修改将不可逆": "此修改将不可逆", "确定是否要复制此渠道？": "确定是否要复制此渠道？", "复制渠道的所有信息": "复制渠道的所有信息", "测试单个渠道操作项目组": "测试单个渠道操作项目组", "禁用": "禁用", "启用": "启用", "启用全部": "启用全部", "禁用全部": "禁用全部", "重置": "重置", "全选": "全选", "_复制": "_复制", "渠道未找到，请刷新页面后重试。": "渠道未找到，请刷新页面后重试。", "渠道复制成功": "渠道复制成功", "渠道复制失败: ": "渠道复制失败: ", "操作成功完成！": "操作成功完成！", "通道 ${name} 测试成功，耗时 ${time.toFixed(2)} 秒。": "通道 ${name} 测试成功，耗时 ${time.toFixed(2)} 秒。", "已停止测试": "已停止测试", "全部": "全部", "请先选择要设置标签的渠道！": "请先选择要设置标签的渠道！", "标签不能为空！": "标签不能为空！", "已为 ${count} 个渠道设置标签！": "已为 ${count} 个渠道设置标签！", "已成功开始测试所有已启用通道，请刷新页面查看结果。": "已成功开始测试所有已启用通道，请刷新页面查看结果。", "已删除所有禁用渠道，共计 ${data} 个": "已删除所有禁用渠道，共计 ${data} 个", "已更新完毕所有已启用通道余额！": "已更新完毕所有已启用通道余额！", "通道 ${name} 余额更新成功！": "通道 ${name} 余额更新成功！", "已删除 ${data} 个通道！": "已删除 ${data} 个通道！", "已修复 ${data} 个通道！": "已修复 ${data} 个通道！", "确定是否要删除所选通道？": "确定是否要删除所选通道？", "删除所选通道": "删除所选通道", "批量设置标签": "批量设置标签", "确定要测试所有通道吗？": "确定要测试所有通道吗？", "测试所有通道": "测试所有通道", "确定要更新所有已启用通道余额吗？": "确定要更新所有已启用通道余额吗？", "更新所有已启用通道余额": "更新所有已启用通道余额", "确定是否要删除禁用通道？": "确定是否要删除禁用通道？", "删除禁用通道": "删除禁用通道", "确定是否要修复数据库一致性？": "确定是否要修复数据库一致性？", "进行该操作时，可能导致渠道访问错误，请仅在数据库出现问题时使用": "进行该操作时，可能导致渠道访问错误，请仅在数据库出现问题时使用", "批量操作": "批量操作", "使用ID排序": "使用ID排序", "开启批量操作": "开启批量操作", "标签聚合模式": "标签聚合模式", "刷新": "刷新", "列设置": "列设置", "搜索渠道的 ID，名称，密钥和API地址 ...": "搜索渠道的 ID，名称，密钥和API地址 ...", "模型关键字": "模型关键字", "选择分组": "选择分组", "查询": "查询", "第 {{start}} - {{end}} 条，共 {{total}} 条": "第 {{start}} - {{end}} 条，共 {{total}} 条", "搜索无结果": "搜索无结果", "请输入要设置的标签名称": "请输入要设置的标签名称", "请输入标签名称": "请输入标签名称", "已选择 ${count} 个渠道": "已选择 ${count} 个渠道", "共": "共", "停止测试": "停止测试", "测试中...": "测试中...", "批量测试${count}个模型": "批量测试${count}个模型", "搜索模型...": "搜索模型...", "模型名称": "模型名称", "测试中": "测试中", "未开始": "未开始", "失败": "失败", "请求时长: ${time}s": "请求时长: ${time}s", "充值": "充值", "消费": "消费", "系统": "系统", "错误": "错误", "流": "流", "非流": "非流", "请求并计费模型": "请求并计费模型", "实际模型": "实际模型", "用户": "用户", "用时/首字": "用时/首字", "提示": "提示", "花费": "花费", "只有当用户设置开启IP记录时，才会进行请求和错误类型日志的IP记录": "只有当用户设置开启IP记录时，才会进行请求和错误类型日志的IP记录", "确定": "确定", "用户信息": "用户信息", "渠道信息": "渠道信息", "语音输入": "语音输入", "文字输入": "文字输入", "文字输出": "文字输出", "缓存创建 Tokens": "缓存创建 Tokens", "日志详情": "日志详情", "消耗额度": "消耗额度", "开始时间": "开始时间", "结束时间": "结束时间", "用户名称": "用户名称", "日志类型": "日志类型", "绘图": "绘图", "放大": "放大", "变换": "变换", "强变换": "强变换", "平移": "平移", "图生文": "图生文", "图混合": "图混合", "重绘": "重绘", "局部重绘-提交": "局部重绘-提交", "自定义变焦-提交": "自定义变焦-提交", "窗口处理": "窗口处理", "未知": "未知", "已提交": "已提交", "等待中": "等待中", "重复提交": "重复提交", "成功": "成功", "未启动": "未启动", "执行中": "执行中", "窗口等待": "窗口等待", "秒": "秒", "提交时间": "提交时间", "花费时间": "花费时间", "任务ID": "任务ID", "提交结果": "提交结果", "任务状态": "任务状态", "结果图片": "结果图片", "查看图片": "查看图片", "无": "无", "失败原因": "失败原因", "已复制：": "已复制：", "当前未开启Midjourney回调，部分项目可能无法获得绘图结果，可在运营设置中开启。": "当前未开启Midjourney回调，部分项目可能无法获得绘图结果，可在运营设置中开启。", "Midjourney 任务记录": "Midjourney 任务记录", "任务 ID": "任务 ID", "按次计费": "按次计费", "按量计费": "按量计费", "您的分组可以使用该模型": "您的分组可以使用该模型", "可用性": "可用性", "计费类型": "计费类型", "当前查看的分组为：{{group}}，倍率为：{{ratio}}": "当前查看的分组为：{{group}}，倍率为：{{ratio}}", "倍率": "倍率", "倍率是为了方便换算不同价格的模型": "倍率是为了方便换算不同价格的模型", "模型倍率": "模型倍率", "补全倍率": "补全倍率", "分组倍率": "分组倍率", "模型价格": "模型价格", "补全": "补全", "模糊搜索模型名称": "模糊搜索模型名称", "复制选中模型": "复制选中模型", "模型定价": "模型定价", "当前分组": "当前分组", "未登录，使用默认分组倍率": "未登录，使用默认分组倍率", "按量计费费用 = 分组倍率 × 模型倍率 × （提示token数 + 补全token数 × 补全倍率）/ 500000 （单位：美元）": "按量计费费用 = 分组倍率 × 模型倍率 × （提示token数 + 补全token数 × 补全倍率）/ 500000 （单位：美元）", "已过期": "已过期", "未使用": "未使用", "已禁用": "已禁用", "创建时间": "创建时间", "过期时间": "过期时间", "永不过期": "永不过期", "确定是否要删除此兑换码？": "确定是否要删除此兑换码？", "查看": "查看", "已复制到剪贴板！": "已复制到剪贴板！", "兑换码可以批量生成和分发，适合用于推广活动或批量充值。": "兑换码可以批量生成和分发，适合用于推广活动或批量充值。", "添加兑换码": "添加兑换码", "请至少选择一个兑换码！": "请至少选择一个兑换码！", "复制所选兑换码到剪贴板": "复制所选兑换码到剪贴板", "确定清除所有失效兑换码？": "确定清除所有失效兑换码？", "将删除已使用、已禁用及过期的兑换码，此操作不可撤销。": "将删除已使用、已禁用及过期的兑换码，此操作不可撤销。", "已删除 {{count}} 条失效兑换码": "已删除 {{count}} 条失效兑换码", "关键字(id或者名称)": "关键字(id或者名称)", "生成音乐": "生成音乐", "生成歌词": "生成歌词", "生成视频": "生成视频", "排队中": "排队中", "正在提交": "正在提交", "平台": "平台", "点击预览视频": "点击预览视频", "任务记录": "任务记录", "渠道 ID": "渠道 ID", "已启用：限制模型": "已启用：限制模型", "已耗尽": "已耗尽", "剩余额度": "剩余额度", "聊天链接配置错误，请联系管理员": "聊天链接配置错误，请联系管理员", "令牌详情": "令牌详情", "确定是否要删除此令牌？": "确定是否要删除此令牌？", "项目操作按钮组": "项目操作按钮组", "请联系管理员配置聊天链接": "请联系管理员配置聊天链接", "令牌用于API访问认证，可以设置额度限制和模型权限。": "令牌用于API访问认证，可以设置额度限制和模型权限。", "添加令牌": "添加令牌", "请至少选择一个令牌！": "请至少选择一个令牌！", "复制所选令牌到剪贴板": "复制所选令牌到剪贴板", "搜索关键字": "搜索关键字", "未知身份": "未知身份", "已封禁": "已封禁", "统计信息": "统计信息", "剩余": "剩余", "调用": "调用", "邀请信息": "邀请信息", "收益": "收益", "无邀请人": "无邀请人", "已注销": "已注销", "确定要提升此用户吗？": "确定要提升此用户吗？", "此操作将提升用户的权限级别": "此操作将提升用户的权限级别", "确定要降级此用户吗？": "确定要降级此用户吗？", "此操作将降低用户的权限级别": "此操作将降低用户的权限级别", "确定是否要注销此用户？": "确定是否要注销此用户？", "相当于删除用户，此修改将不可逆": "相当于删除用户，此修改将不可逆", "用户管理页面，可以查看和管理所有注册用户的信息、权限和状态。": "用户管理页面，可以查看和管理所有注册用户的信息、权限和状态。", "添加用户": "添加用户", "支持搜索用户的 ID、用户名、显示名称和邮箱地址": "支持搜索用户的 ID、用户名、显示名称和邮箱地址", "全部模型": "全部模型", "智谱": "智谱", "通义千问": "通义千问", "文心一言": "文心一言", "腾讯混元": "腾讯混元", "360智脑": "360智脑", "豆包": "豆包", "用户分组": "用户分组", "专属倍率": "专属倍率", "输入价格：${{price}} / 1M tokens{{audioPrice}}": "输入价格：${{price}} / 1M tokens{{audioPrice}}", "Web搜索价格：${{price}} / 1K 次": "Web搜索价格：${{price}} / 1K 次", "文件搜索价格：${{price}} / 1K 次": "文件搜索价格：${{price}} / 1K 次", "仅供参考，以实际扣费为准": "仅供参考，以实际扣费为准", "价格：${{price}} * {{ratioType}}：{{ratio}}": "价格：${{price}} * {{ratioType}}：{{ratio}}", "模型: {{ratio}} * {{ratioType}}：{{groupRatio}}": "模型: {{ratio}} * {{ratioType}}：{{groupRatio}}", "提示价格：${{price}} / 1M tokens": "提示价格：${{price}} / 1M tokens", "模型价格 ${{price}}，{{ratioType}} {{ratio}}": "模型价格 ${{price}}，{{ratioType}} {{ratio}}", "模型: {{ratio}} * {{ratioType}}: {{groupRatio}}": "模型: {{ratio}} * {{ratioType}}: {{groupRatio}}", "不是合法的 JSON 字符串": "不是合法的 JSON 字符串", "请求发生错误: ": "请求发生错误: ", "解析响应数据时发生错误": "解析响应数据时发生错误", "连接已断开": "连接已断开", "建立连接时发生错误": "建立连接时发生错误", "加载模型失败": "加载模型失败", "加载分组失败": "加载分组失败", "消息已复制到剪贴板": "消息已复制到剪贴板", "确认删除": "确认删除", "确定要删除这条消息吗？": "确定要删除这条消息吗？", "已删除消息及其回复": "已删除消息及其回复", "消息已删除": "消息已删除", "消息已编辑": "消息已编辑", "检测到该消息后有AI回复，是否删除后续回复并重新生成？": "检测到该消息后有AI回复，是否删除后续回复并重新生成？", "重新生成": "重新生成", "消息已更新": "消息已更新", "加载关于内容失败...": "加载关于内容失败...", "可在设置页面设置关于内容，支持 HTML & Markdown": "可在设置页面设置关于内容，支持 HTML & Markdown", "New API项目仓库地址：": "New API项目仓库地址：", "| 基于": "| 基于", "本项目根据": "本项目根据", "MIT许可证": "MIT许可证", "授权，需在遵守": "授权，需在遵守", "Apache-2.0协议": "Apache-2.0协议", "管理员暂时未设置任何关于内容": "管理员暂时未设置任何关于内容", "仅支持 OpenAI 接口格式": "仅支持 OpenAI 接口格式", "请填写密钥": "请填写密钥", "获取模型列表成功": "获取模型列表成功", "获取模型列表失败": "获取模型列表失败", "请填写渠道名称和渠道密钥！": "请填写渠道名称和渠道密钥！", "请至少选择一个模型！": "请至少选择一个模型！", "模型映射必须是合法的 JSON 格式！": "模型映射必须是合法的 JSON 格式！", "提交失败，请勿重复提交！": "提交失败，请勿重复提交！", "渠道创建成功！": "渠道创建成功！", "已新增 {{count}} 个模型：{{list}}": "已新增 {{count}} 个模型：{{list}}", "未发现新增模型": "未发现新增模型", "新建": "新建", "更新渠道信息": "更新渠道信息", "创建新的渠道": "创建新的渠道", "基本信息": "基本信息", "渠道的基本配置信息": "渠道的基本配置信息", "请选择渠道类型": "请选择渠道类型", "请为渠道命名": "请为渠道命名", "请输入密钥，一行一个": "请输入密钥，一行一个", "批量创建": "批量创建", "API 配置": "API 配置", "API 地址和相关配置": "API 地址和相关配置", "2025年5月10日后添加的渠道，不需要再在部署的时候移除模型名称中的\".\"": "2025年5月10日后添加的渠道，不需要再在部署的时候移除模型名称中的\".\"", "请输入 AZURE_OPENAI_ENDPOINT，例如：https://docs-test-001.openai.azure.com": "请输入 AZURE_OPENAI_ENDPOINT，例如：https://docs-test-001.openai.azure.com", "请输入默认 API 版本，例如：2025-04-01-preview": "请输入默认 API 版本，例如：2025-04-01-preview", "如果你对接的是上游One API或者New API等转发项目，请使用OpenAI类型，不要使用此类型，除非你知道你在做什么。": "如果你对接的是上游One API或者New API等转发项目，请使用OpenAI类型，不要使用此类型，除非你知道你在做什么。", "完整的 Base URL，支持变量{model}": "完整的 Base URL，支持变量{model}", "请输入完整的URL，例如：https://api.openai.com/v1/chat/completions": "请输入完整的URL，例如：https://api.openai.com/v1/chat/completions", "Dify渠道只适配chatflow和agent，并且agent不支持图片！": "Dify渠道只适配chatflow和agent，并且agent不支持图片！", "此项可选，用于通过自定义API地址来进行 API 调用，末尾不要带/v1和/": "此项可选，用于通过自定义API地址来进行 API 调用，末尾不要带/v1和/", "对于官方渠道，new-api已经内置地址，除非是第三方代理站点或者Azure的特殊接入地址，否则不需要填写": "对于官方渠道，new-api已经内置地址，除非是第三方代理站点或者Azure的特殊接入地址，否则不需要填写", "私有部署地址": "私有部署地址", "请输入私有部署地址，格式为：https://fastgpt.run/api/openapi": "请输入私有部署地址，格式为：https://fastgpt.run/api/openapi", "注意非Chat API，请务必填写正确的API地址，否则可能导致无法使用": "注意非Chat API，请务必填写正确的API地址，否则可能导致无法使用", "请输入到 /suno 前的路径，通常就是域名，例如：https://api.example.com": "请输入到 /suno 前的路径，通常就是域名，例如：https://api.example.com", "模型选择和映射设置": "模型选择和映射设置", "模型": "模型", "请选择该渠道所支持的模型": "请选择该渠道所支持的模型", "填入相关模型": "填入相关模型", "填入所有模型": "填入所有模型", "获取模型列表": "获取模型列表", "清除所有模型": "清除所有模型", "输入自定义模型名称": "输入自定义模型名称", "模型重定向": "模型重定向", "此项可选，用于修改请求体中的模型名称，为一个 JSON 字符串，键为请求中模型名称，值为要替换的模型名称，例如：": "此项可选，用于修改请求体中的模型名称，为一个 JSON 字符串，键为请求中模型名称，值为要替换的模型名称，例如：", "填入模板": "填入模板", "默认测试模型": "默认测试模型", "不填则为模型列表第一个": "不填则为模型列表第一个", "渠道的高级配置选项": "渠道的高级配置选项", "请选择可以使用该渠道的分组": "请选择可以使用该渠道的分组", "请在系统设置页面编辑分组倍率以添加新的分组：": "请在系统设置页面编辑分组倍率以添加新的分组：", "部署地区": "部署地区", "知识库 ID": "知识库 ID", "渠道标签": "渠道标签", "渠道优先级": "渠道优先级", "渠道权重": "渠道权重", "渠道额外设置": "渠道额外设置", "此项可选，用于配置渠道特定设置，为一个 JSON 字符串，例如：": "此项可选，用于配置渠道特定设置，为一个 JSON 字符串，例如：", "参数覆盖": "参数覆盖", "此项可选，用于覆盖请求参数。不支持覆盖 stream 参数。为一个 JSON 字符串，例如：": "此项可选，用于覆盖请求参数。不支持覆盖 stream 参数。为一个 JSON 字符串，例如：", "请输入组织org-xxx": "请输入组织org-xxx", "组织，可选，不填则为默认组织": "组织，可选，不填则为默认组织", "是否自动禁用（仅当自动禁用开启时有效），关闭后不会自动禁用该渠道": "是否自动禁用（仅当自动禁用开启时有效），关闭后不会自动禁用该渠道", "状态码复写（仅影响本地判断，不修改返回到上游的状态码）": "状态码复写（仅影响本地判断，不修改返回到上游的状态码）", "此项可选，用于复写返回的状态码，比如将claude渠道的400错误复写为500（用于重试），请勿滥用该功能，例如：": "此项可选，用于复写返回的状态码，比如将claude渠道的400错误复写为500（用于重试），请勿滥用该功能，例如：", "编辑标签": "编辑标签", "标签信息": "标签信息", "标签的基本配置": "标签的基本配置", "所有编辑均为覆盖操作，留空则不更改": "所有编辑均为覆盖操作，留空则不更改", "标签名称": "标签名称", "请输入新标签，留空则解散标签": "请输入新标签，留空则解散标签", "当前模型列表为该标签下所有渠道模型列表最长的一个，并非所有渠道的并集，请注意可能导致某些渠道模型丢失。": "当前模型列表为该标签下所有渠道模型列表最长的一个，并非所有渠道的并集，请注意可能导致某些渠道模型丢失。", "请选择该渠道所支持的模型，留空则不更改": "请选择该渠道所支持的模型，留空则不更改", "此项可选，用于修改请求体中的模型名称，为一个 JSON 字符串，键为请求中模型名称，值为要替换的模型名称，留空则不更改": "此项可选，用于修改请求体中的模型名称，为一个 JSON 字符串，键为请求中模型名称，值为要替换的模型名称，留空则不更改", "清空重定向": "清空重定向", "分组设置": "分组设置", "用户分组配置": "用户分组配置", "请选择可以使用该渠道的分组，留空则不更改": "请选择可以使用该渠道的分组，留空则不更改", "正在跳转...": "正在跳转...", "小时": "小时", "周": "周", "模型调用次数占比": "模型调用次数占比", "模型消耗分布": "模型消耗分布", "总计": "总计", "早上好": "早上好", "中午好": "中午好", "下午好": "下午好", "账户数据": "账户数据", "使用统计": "使用统计", "统计次数": "统计次数", "资源消耗": "资源消耗", "统计额度": "统计额度", "性能指标": "性能指标", "平均RPM": "平均RPM", "复制成功": "复制成功", "进行中": "进行中", "异常": "异常", "正常": "正常", "可用率": "可用率", "有异常": "有异常", "高延迟": "高延迟", "维护中": "维护中", "暂无监控数据": "暂无监控数据", "搜索条件": "搜索条件", "时间粒度": "时间粒度", "模型数据分析": "模型数据分析", "消耗分布": "消耗分布", "调用次数分布": "调用次数分布", "API信息": "API信息", "暂无API信息": "暂无API信息", "请联系管理员在系统设置中配置API信息": "请联系管理员在系统设置中配置API信息", "显示最新20条": "显示最新20条", "请联系管理员在系统设置中配置公告信息": "请联系管理员在系统设置中配置公告信息", "暂无常见问答": "暂无常见问答", "请联系管理员在系统设置中配置常见问答": "请联系管理员在系统设置中配置常见问答", "服务可用性": "服务可用性", "请联系管理员在系统设置中配置Uptime": "请联系管理员在系统设置中配置Uptime", "加载首页内容失败...": "加载首页内容失败...", "统一的大模型接口网关": "统一的大模型接口网关", "更好的价格，更好的稳定性，无需订阅": "更好的价格，更好的稳定性，无需订阅", "开始使用": "开始使用", "支持众多的大模型供应商": "支持众多的大模型供应商", "页面未找到，请检查您的浏览器地址是否正确": "页面未找到，请检查您的浏览器地址是否正确", "登录过期，请重新登录！": "登录过期，请重新登录！", "兑换码更新成功！": "兑换码更新成功！", "兑换码创建成功！": "兑换码创建成功！", "兑换码创建成功": "兑换码创建成功", "兑换码创建成功，是否下载兑换码？": "兑换码创建成功，是否下载兑换码？", "兑换码将以文本文件的形式下载，文件名为兑换码的名称。": "兑换码将以文本文件的形式下载，文件名为兑换码的名称。", "更新兑换码信息": "更新兑换码信息", "创建新的兑换码": "创建新的兑换码", "设置兑换码的基本信息": "设置兑换码的基本信息", "请输入名称": "请输入名称", "选择过期时间（可选，留空为永久）": "选择过期时间（可选，留空为永久）", "额度设置": "额度设置", "设置兑换码的额度和数量": "设置兑换码的额度和数量", "请输入额度": "请输入额度", "生成数量": "生成数量", "请输入生成数量": "请输入生成数量", "你似乎并没有修改什么": "你似乎并没有修改什么", "部分保存失败，请重试": "部分保存失败，请重试", "保存成功": "保存成功", "保存失败，请重试": "保存失败，请重试", "请检查输入": "请检查输入", "聊天配置": "聊天配置", "为一个 JSON 文本": "为一个 JSON 文本", "保存聊天设置": "保存聊天设置", "设置已保存": "设置已保存", "API地址": "API地址", "说明": "说明", "颜色": "颜色", "API信息管理，可以配置多个API地址用于状态展示和负载均衡（最多50个）": "API信息管理，可以配置多个API地址用于状态展示和负载均衡（最多50个）", "批量删除": "批量删除", "保存设置": "保存设置", "添加API": "添加API", "请输入API地址": "请输入API地址", "如：香港线路": "如：香港线路", "请输入线路描述": "请输入线路描述", "如：大带宽批量分析图片推荐": "如：大带宽批量分析图片推荐", "请输入说明": "请输入说明", "标识颜色": "标识颜色", "确定要删除此API信息吗？": "确定要删除此API信息吗？", "警告": "警告", "发布时间": "发布时间", "操作": "操作", "系统公告管理，可以发布系统通知和重要消息（最多100个，前端显示最新20条）": "系统公告管理，可以发布系统通知和重要消息（最多100个，前端显示最新20条）", "添加公告": "添加公告", "编辑公告": "编辑公告", "公告内容": "公告内容", "请输入公告内容": "请输入公告内容", "请选择发布日期": "请选择发布日期", "公告类型": "公告类型", "说明信息": "说明信息", "可选，公告的补充说明": "可选，公告的补充说明", "确定要删除此公告吗？": "确定要删除此公告吗？", "数据看板设置": "数据看板设置", "启用数据看板（实验性）": "启用数据看板（实验性）", "数据看板更新间隔": "数据看板更新间隔", "设置过短会影响数据库性能": "设置过短会影响数据库性能", "数据看板默认时间粒度": "数据看板默认时间粒度", "仅修改展示粒度，统计精确到小时": "仅修改展示粒度，统计精确到小时", "保存数据看板设置": "保存数据看板设置", "问题标题": "问题标题", "回答内容": "回答内容", "常见问答管理，为用户提供常见问题的答案（最多50个，前端显示最新20条）": "常见问答管理，为用户提供常见问题的答案（最多50个，前端显示最新20条）", "添加问答": "添加问答", "编辑问答": "编辑问答", "请输入问题标题": "请输入问题标题", "请输入回答内容": "请输入回答内容", "确定要删除此问答吗？": "确定要删除此问答吗？", "分类名称": "分类名称", "Uptime Kuma地址": "Uptime Kuma地址", "Uptime Kuma监控分类管理，可以配置多个监控分类用于服务状态展示（最多20个）": "Uptime Kuma监控分类管理，可以配置多个监控分类用于服务状态展示（最多20个）", "编辑分类": "编辑分类", "添加分类": "添加分类", "请输入分类名称，如：OpenAI、Claude等": "请输入分类名称，如：OpenAI、<PERSON>等", "请输入分类名称": "请输入分类名称", "请输入Uptime Kuma服务地址，如：https://status.example.com": "请输入Uptime Kuma服务地址，如：https://status.example.com", "请输入Uptime Kuma地址": "请输入Uptime Kuma地址", "请输入状态页面的Slug，如：my-status": "请输入状态页面的Slug，如：my-status", "请输入状态页面Slug": "请输入状态页面Slug", "确定要删除此分类吗？": "确定要删除此分类吗？", "绘图设置": "绘图设置", "启用绘图功能": "启用绘图功能", "允许回调（会泄露服务器 IP 地址）": "允许回调（会泄露服务器 IP 地址）", "允许 AccountFilter 参数": "允许 AccountFilter 参数", "开启之后会清除用户提示词中的": "开启之后会清除用户提示词中的", "以及": "以及", "检测必须等待绘图成功才能进行放大等操作": "检测必须等待绘图成功才能进行放大等操作", "保存绘图设置": "保存绘图设置", "Claude设置": "<PERSON>设置", "Claude请求头覆盖": "Claude请求头覆盖", "为一个 JSON 文本，例如：": "为一个 JSON 文本，例如：", "缺省 MaxTokens": "缺省 MaxTokens", "启用Claude思考适配（-thinking后缀）": "启用Claude思考适配（-thinking后缀）", "思考适配 BudgetTokens 百分比": "思考适配 BudgetTokens 百分比", "0.1-1之间的小数": "0.1-1之间的小数", "Gemini设置": "Gemini设置", "Gemini安全设置": "Gemini安全设置", "default为默认设置，可单独设置每个模型的版本": "default为默认设置，可单独设置每个模型的版本", "例如：": "例如：", "Gemini思考适配设置": "Gemini思考适配设置", "启用Gemini思考后缀适配": "启用Gemini思考后缀适配", "适配 -thinking、-thinking-预算数字 和 -nothinking 后缀": "适配 -thinking、-thinking-预算数字 和 -nothinking 后缀", "0.002-1之间的小数": "0.002-1之间的小数", "全局设置": "全局设置", "启用请求透传": "启用请求透传", "连接保活设置": "连接保活设置", "启用Ping间隔": "启用Ping间隔", "Ping间隔（秒）": "<PERSON>间隔（秒）", "新用户初始额度": "新用户初始额度", "请求预扣费额度": "请求预扣费额度", "请求结束后多退少补": "请求结束后多退少补", "邀请新用户奖励额度": "邀请新用户奖励额度", "新用户使用邀请码奖励额度": "新用户使用邀请码奖励额度", "例如：1000": "例如：1000", "保存额度设置": "保存额度设置", "例如发卡网站的购买链接": "例如发卡网站的购买链接", "文档地址": "文档地址", "单位美元额度": "单位美元额度", "一单位货币能兑换的额度": "一单位货币能兑换的额度", "失败重试次数": "失败重试次数", "以货币形式显示额度": "以货币形式显示额度", "额度查询接口返回令牌额度而非用户额度": "额度查询接口返回令牌额度而非用户额度", "默认折叠侧边栏": "默认折叠侧边栏", "开启后不限制：必须设置模型倍率": "开启后不限制：必须设置模型倍率", "保存通用设置": "保存通用设置", "请选择日志记录时间": "请选择日志记录时间", "条日志已清理！": "条日志已清理！", "日志清理失败：": "日志清理失败：", "启用额度消费日志记录": "启用额度消费日志记录", "日志记录时间": "日志记录时间", "清除历史日志": "清除历史日志", "保存日志设置": "保存日志设置", "监控设置": "监控设置", "测试所有渠道的最长响应时间": "测试所有渠道的最长响应时间", "额度提醒阈值": "额度提醒阈值", "低于此额度时将发送邮件提醒用户": "低于此额度时将发送邮件提醒用户", "失败时自动禁用通道": "失败时自动禁用通道", "成功时自动启用通道": "成功时自动启用通道", "自动禁用关键词": "自动禁用关键词", "一行一个，不区分大小写": "一行一个，不区分大小写", "屏蔽词过滤设置": "屏蔽词过滤设置", "启用屏蔽词过滤功能": "启用屏蔽词过滤功能", "启用 Prompt 检查": "启用 Prompt 检查", "一行一个屏蔽词，不需要符号分割": "一行一个屏蔽词，不需要符号分割", "保存屏蔽词过滤设置": "保存屏蔽词过滤设置", "更新成功": "更新成功", "更新失败": "更新失败", "服务器地址": "服务器地址", "更新服务器地址": "更新服务器地址", "请先填写服务器地址": "请先填写服务器地址", "充值分组倍率不是合法的 JSON 字符串": "充值分组倍率不是合法的 JSON 字符串", "充值方式设置不是合法的 JSON 字符串": "充值方式设置不是合法的 JSON 字符串", "支付设置": "支付设置", "（当前仅支持易支付接口，默认使用上方服务器地址作为回调地址！）": "（当前仅支持易支付接口，默认使用上方服务器地址作为回调地址！）", "例如：https://yourdomain.com": "例如：https://yourdomain.com", "易支付商户ID": "易支付商户ID", "易支付商户密钥": "易支付商户密钥", "敏感信息不会发送到前端显示": "敏感信息不会发送到前端显示", "回调地址": "回调地址", "充值价格（x元/美金）": "充值价格（x元/美金）", "例如：7，就是7元/美金": "例如：7，就是7元/美金", "最低充值美元数量": "最低充值美元数量", "例如：2，就是最低充值2$": "例如：2，就是最低充值2$", "为一个 JSON 文本，键为组名称，值为倍率": "为一个 JSON 文本，键为组名称，值为倍率", "充值方式设置": "充值方式设置", "更新支付设置": "更新支付设置", "模型请求速率限制": "模型请求速率限制", "启用用户模型请求速率限制（可能会影响高并发性能）": "启用用户模型请求速率限制（可能会影响高并发性能）", "分钟": "分钟", "频率限制的周期（分钟）": "频率限制的周期（分钟）", "用户每周期最多请求次数": "用户每周期最多请求次数", "包括失败请求的次数，0代表不限制": "包括失败请求的次数，0代表不限制", "用户每周期最多请求完成次数": "用户每周期最多请求完成次数", "只包括请求成功的次数": "只包括请求成功的次数", "分组速率限制": "分组速率限制", "使用 JSON 对象格式，格式为：{\"组名\": [最多请求次数, 最多请求完成次数]}": "使用 JSON 对象格式，格式为：{\"组名\": [最多请求次数, 最多请求完成次数]}", "示例：{\"default\": [200, 100], \"vip\": [0, 1000]}。": "示例：{\"default\": [200, 100], \"vip\": [0, 1000]}。", "[最多请求次数]必须大于等于0，[最多请求完成次数]必须大于等于1。": "[最多请求次数]必须大于等于0，[最多请求完成次数]必须大于等于1。", "分组速率配置优先级高于全局速率限制。": "分组速率配置优先级高于全局速率限制。", "限制周期统一使用上方配置的“限制周期”值。": "限制周期统一使用上方配置的“限制周期”值。", "保存模型速率限制": "保存模型速率限制", "保存失败": "保存失败", "为一个 JSON 文本，键为分组名称，值为倍率": "为一个 JSON 文本，键为分组名称，值为倍率", "用户可选分组": "用户可选分组", "为一个 JSON 文本，键为分组名称，值为分组描述": "为一个 JSON 文本，键为分组名称，值为分组描述", "自动分组auto，从第一个开始选择": "自动分组auto，从第一个开始选择", "必须是有效的 JSON 字符串数组，例如：[\"g1\",\"g2\"]": "必须是有效的 JSON 字符串数组，例如：[\"g1\",\"g2\"]", "模型固定价格": "模型固定价格", "一次调用消耗多少刀，优先级大于模型倍率": "一次调用消耗多少刀，优先级大于模型倍率", "为一个 JSON 文本，键为模型名称，值为倍率": "为一个 JSON 文本，键为模型名称，值为倍率", "模型补全倍率（仅对自定义模型有效）": "模型补全倍率（仅对自定义模型有效）", "仅对自定义模型有效": "仅对自定义模型有效", "保存模型倍率设置": "保存模型倍率设置", "确定重置模型倍率吗？": "确定重置模型倍率吗？", "重置模型倍率": "重置模型倍率", "获取启用模型失败:": "获取启用模型失败:", "获取启用模型失败": "获取启用模型失败", "JSON解析错误:": "JSON解析错误:", "保存失败:": "保存失败:", "输入模型倍率": "输入模型倍率", "输入补全倍率": "输入补全倍率", "请输入数字": "请输入数字", "模型名称已存在": "模型名称已存在", "请先选择需要批量设置的模型": "请先选择需要批量设置的模型", "请输入模型倍率和补全倍率": "请输入模型倍率和补全倍率", "请输入有效的数字": "请输入有效的数字", "请输入填充值": "请输入填充值", "批量设置成功": "批量设置成功", "已为 {{count}} 个模型设置{{type}}": "已为 {{count}} 个模型设置{{type}}", "模型倍率和补全倍率": "模型倍率和补全倍率", "添加模型": "添加模型", "批量设置": "批量设置", "应用更改": "应用更改", "搜索模型名称": "搜索模型名称", "此页面仅显示未设置价格或倍率的模型，设置后将自动从列表中移除": "此页面仅显示未设置价格或倍率的模型，设置后将自动从列表中移除", "定价模式": "定价模式", "固定价格": "固定价格", "固定价格(每次)": "固定价格(每次)", "输入每次价格": "输入每次价格", "输入补全价格": "输入补全价格", "批量设置模型参数": "批量设置模型参数", "设置类型": "设置类型", "模型倍率和补全倍率同时设置": "模型倍率和补全倍率同时设置", "模型倍率值": "模型倍率值", "请输入模型倍率": "请输入模型倍率", "补全倍率值": "补全倍率值", "请输入补全倍率": "请输入补全倍率", "请输入数值": "请输入数值", "将为选中的 ": "将为选中的 ", " 个模型设置相同的值": " 个模型设置相同的值", "当前设置类型: ": "当前设置类型: ", "默认补全倍率": "默认补全倍率", "添加成功": "添加成功", "价格设置方式": "价格设置方式", "按倍率设置": "按倍率设置", "按价格设置": "按价格设置", "输入价格": "输入价格", "输出价格": "输出价格", "获取渠道失败：": "获取渠道失败：", "请至少选择一个渠道": "请至少选择一个渠道", "后端请求失败": "后端请求失败", "部分渠道测试失败：": "部分渠道测试失败：", "未找到差异化倍率，无需同步": "未找到差异化倍率，无需同步", "请求后端接口失败：": "请求后端接口失败：", "同步成功": "同步成功", "部分保存失败": "部分保存失败", "未找到匹配的模型": "未找到匹配的模型", "暂无差异化倍率显示": "暂无差异化倍率显示", "请先选择同步渠道": "请先选择同步渠道", "倍率类型": "倍率类型", "缓存倍率": "缓存倍率", "当前值": "当前值", "未设置": "未设置", "与本地相同": "与本地相同", "运营设置": "运营设置", "聊天设置": "聊天设置", "速率限制设置": "速率限制设置", "模型相关设置": "模型相关设置", "系统设置": "系统设置", "仪表盘设置": "仪表盘设置", "获取初始化状态失败": "获取初始化状态失败", "表单引用错误，请刷新页面重试": "表单引用错误，请刷新页面重试", "请输入管理员用户名": "请输入管理员用户名", "密码长度至少为8个字符": "密码长度至少为8个字符", "两次输入的密码不一致": "两次输入的密码不一致", "系统初始化成功，正在跳转...": "系统初始化成功，正在跳转...", "初始化失败，请重试": "初始化失败，请重试", "系统初始化失败，请重试": "系统初始化失败，请重试", "系统初始化": "系统初始化", "欢迎使用，请完成以下设置以开始使用系统": "欢迎使用，请完成以下设置以开始使用系统", "数据库信息": "数据库信息", "管理员账号": "管理员账号", "设置系统管理员的登录信息": "设置系统管理员的登录信息", "管理员账号已经初始化过，请继续设置其他参数": "管理员账号已经初始化过，请继续设置其他参数", "密码": "密码", "请输入管理员密码": "请输入管理员密码", "请确认管理员密码": "请确认管理员密码", "选择适合您使用场景的模式": "选择适合您使用场景的模式", "对外运营模式": "对外运营模式", "适用于为多个用户提供服务的场景": "适用于为多个用户提供服务的场景", "默认模式": "默认模式", "适用于个人使用的场景，不需要设置模型价格": "适用于个人使用的场景，不需要设置模型价格", "无需计费": "无需计费", "演示站点模式": "演示站点模式", "适用于展示系统功能的场景，提供基础功能演示": "适用于展示系统功能的场景，提供基础功能演示", "初始化系统": "初始化系统", "使用模式说明": "使用模式说明", "我已了解": "我已了解", "默认模式，适用于为多个用户提供服务的场景。": "默认模式，适用于为多个用户提供服务的场景。", "此模式下，系统将计算每次调用的用量，您需要对每个模型都设置价格，如果没有设置价格，用户将无法使用该模型。": "此模式下，系统将计算每次调用的用量，您需要对每个模型都设置价格，如果没有设置价格，用户将无法使用该模型。", "多用户支持": "多用户支持", "适用于个人使用的场景。": "适用于个人使用的场景。", "不需要设置模型价格，系统将弱化用量计算，您可专注于使用模型。": "不需要设置模型价格，系统将弱化用量计算，您可专注于使用模型。", "个人使用": "个人使用", "适用于展示系统功能的场景。": "适用于展示系统功能的场景。", "提供基础功能演示，方便用户了解系统特性。": "提供基础功能演示，方便用户了解系统特性。", "体验试用": "体验试用", "自动选择": "自动选择", "过期时间格式错误！": "过期时间格式错误！", "令牌更新成功！": "令牌更新成功！", "令牌创建成功，请在列表页面点击复制获取令牌！": "令牌创建成功，请在列表页面点击复制获取令牌！", "更新令牌信息": "更新令牌信息", "创建新的令牌": "创建新的令牌", "设置令牌的基本信息": "设置令牌的基本信息", "请选择过期时间": "请选择过期时间", "一天": "一天", "一个月": "一个月", "设置令牌可用额度和数量": "设置令牌可用额度和数量", "新建数量": "新建数量", "请选择或输入创建令牌的数量": "请选择或输入创建令牌的数量", "20个": "20个", "100个": "100个", "取消无限额度": "取消无限额度", "设为无限额度": "设为无限额度", "设置令牌的访问限制": "设置令牌的访问限制", "IP白名单": "IP白名单", "允许的IP，一行一个，不填写则不限制": "允许的IP，一行一个，不填写则不限制", "请勿过度信任此功能，IP可能被伪造": "请勿过度信任此功能，IP可能被伪造", "勾选启用模型限制后可选择": "勾选启用模型限制后可选择", "非必要，不建议启用模型限制": "非必要，不建议启用模型限制", "分组信息": "分组信息", "设置令牌的分组": "设置令牌的分组", "令牌分组，默认为用户的分组": "令牌分组，默认为用户的分组", "管理员未设置用户可选分组": "管理员未设置用户可选分组", "请输入兑换码！": "请输入兑换码！", "兑换成功！": "兑换成功！", "成功兑换额度：": "成功兑换额度：", "请求失败": "请求失败", "超级管理员未设置充值链接！": "超级管理员未设置充值链接！", "管理员未开启在线充值！": "管理员未开启在线充值！", "充值数量不能小于": "充值数量不能小于", "支付请求失败": "支付请求失败", "划转金额最低为": "划转金额最低为", "邀请链接已复制到剪切板": "邀请链接已复制到剪切板", "支付方式配置错误, 请联系管理员": "支付方式配置错误, 请联系管理员", "划转邀请额度": "划转邀请额度", "可用邀请额度": "可用邀请额度", "划转额度": "划转额度", "充值确认": "充值确认", "充值数量": "充值数量", "实付金额": "实付金额", "支付方式": "支付方式", "在线充值": "在线充值", "快速方便的充值方式": "快速方便的充值方式", "选择充值额度": "选择充值额度", "实付": "实付", "或输入自定义金额": "或输入自定义金额", "充值数量，最低 ": "充值数量，最低 ", "选择支付方式": "选择支付方式", "处理中": "处理中", "兑换码充值": "兑换码充值", "使用兑换码快速充值": "使用兑换码快速充值", "请输入兑换码": "请输入兑换码", "兑换中...": "兑换中...", "兑换": "兑换", "邀请奖励": "邀请奖励", "邀请好友获得额外奖励": "邀请好友获得额外奖励", "待使用收益": "待使用收益", "总收益": "总收益", "邀请人数": "邀请人数", "邀请链接": "邀请链接", "邀请好友注册，好友充值后您可获得相应奖励": "邀请好友注册，好友充值后您可获得相应奖励", "通过划转功能将奖励额度转入到您的账户余额中": "通过划转功能将奖励额度转入到您的账户余额中", "邀请的好友越多，获得的奖励越多": "邀请的好友越多，获得的奖励越多", "用户名和密码不能为空！": "用户名和密码不能为空！", "用户账户创建成功！": "用户账户创建成功！", "提交": "提交", "创建新用户账户": "创建新用户账户", "请输入显示名称": "请输入显示名称", "请输入密码": "请输入密码", "请输入备注（仅管理员可见）": "请输入备注（仅管理员可见）", "编辑用户": "编辑用户", "用户的基本账户信息": "用户的基本账户信息", "请输入新的用户名": "请输入新的用户名", "请输入新的密码，最短 8 位": "请输入新的密码，最短 8 位", "显示名称": "显示名称", "请输入新的显示名称": "请输入新的显示名称", "权限设置": "权限设置", "用户分组和额度管理": "用户分组和额度管理", "请输入新的剩余额度": "请输入新的剩余额度", "添加额度": "添加额度", "第三方账户绑定状态（只读）": "第三方账户绑定状态（只读）", "已绑定的 GitHub 账户": "已绑定的 GitHub 账户", "已绑定的 OIDC 账户": "已绑定的 OIDC 账户", "已绑定的微信账户": "已绑定的微信账户", "已绑定的邮箱账户": "已绑定的邮箱账户", "已绑定的 Telegram 账户": "已绑定的 Telegram 账户", "新额度": "新额度", "需要添加的额度（支持负数）": "需要添加的额度（支持负数）"}