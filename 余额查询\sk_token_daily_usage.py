#!/usr/bin/env python3
"""
SK Token Daily Usage Query Module
通过 sk- 令牌直接查询今日消耗的模块

核心思路：
1. 利用 new-api 的 TokenAuth 中间件，通过 sk- 令牌进行认证
2. 调用需要 TokenAuth 的API端点来获取统计信息
3. 由于 TokenAuth 会自动从 sk- 令牌中提取用户信息，无需额外的 access_token 和 user_id

可用的 TokenAuth 保护的端点：
- /v1/dashboard/billing/subscription (获取额度信息)
- /v1/dashboard/billing/usage (获取使用量信息)

作者：Claude 4.0 Sonnet 🐾
"""

import os
import requests
import json
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional


def mask_key(key: str) -> str:
    """Mask API key for display"""
    if not key:
        return ""
    return "***" if len(key) <= 8 else f"{key[:4]}...{key[-4:]}"


def get_today_date_range() -> tuple[str, str]:
    """
    Get today's date range in YYYY-MM-DD format
    获取今天的日期范围（YYYY-MM-DD格式）
    """
    today = date.today()
    return today.strftime("%Y-%m-%d"), today.strftime("%Y-%m-%d")


def query_subscription_info(base_url: str, token_key: str) -> Dict[str, Any]:
    """
    Query subscription information using TokenAuth
    通过 TokenAuth 查询订阅信息
    
    Args:
        base_url: API base URL (e.g., "https://api.pipidan.xyz")
        token_key: Token key (sk-xxx format)
    
    Returns:
        Dictionary containing subscription info or error
    """
    try:
        api_url = f"{base_url.rstrip('/')}/v1/dashboard/billing/subscription"
        headers = {"Authorization": f"Bearer {token_key}"}
        
        print(f"🔍 Querying subscription: {api_url}")
        print(f"🔑 Token: {mask_key(token_key)}")
        
        response = requests.get(api_url, headers=headers, timeout=15)
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            return {"success": True, "data": data}
        else:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text[:200]}",
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Request failed: {str(e)}",
        }


def query_usage_info(base_url: str, token_key: str, start_date: str, end_date: str) -> Dict[str, Any]:
    """
    Query usage information using TokenAuth
    通过 TokenAuth 查询使用量信息
    
    Args:
        base_url: API base URL
        token_key: Token key (sk-xxx format)
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
    
    Returns:
        Dictionary containing usage info or error
    """
    try:
        api_url = f"{base_url.rstrip('/')}/v1/dashboard/billing/usage"
        headers = {"Authorization": f"Bearer {token_key}"}
        params = {
            "start_date": start_date,
            "end_date": end_date,
        }
        
        print(f"🔍 Querying usage: {api_url}")
        print(f"📅 Date range: {start_date} to {end_date}")
        
        response = requests.get(api_url, headers=headers, params=params, timeout=15)
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            return {"success": True, "data": data}
        else:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text[:200]}",
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Request failed: {str(e)}",
        }


def query_daily_usage_by_sk_token(base_url: str, token_key: str) -> Dict[str, Any]:
    """
    Main function to query daily usage by SK token
    通过 SK 令牌查询今日使用量的主函数
    
    Args:
        base_url: API base URL
        token_key: Token key (sk-xxx format)
    
    Returns:
        Dictionary containing daily usage statistics or error info
    """
    # Step 1: Get subscription info (total quota)
    subscription_result = query_subscription_info(base_url, token_key)
    
    if not subscription_result.get("success"):
        return subscription_result
    
    subscription_data = subscription_result.get("data", {})
    total_quota_usd = float(subscription_data.get("hard_limit_usd", 0))
    
    # Step 2: Get today's usage
    start_date, end_date = get_today_date_range()
    usage_result = query_usage_info(base_url, token_key, start_date, end_date)
    
    if not usage_result.get("success"):
        return usage_result
    
    usage_data = usage_result.get("data", {})
    
    # Extract usage information
    total_usage_cents = usage_data.get("total_usage", 0)  # in cents
    daily_usage_usd = total_usage_cents / 100.0  # convert to dollars
    
    # Calculate remaining quota
    is_unlimited = total_quota_usd >= 100000000  # Very large number indicates unlimited
    if is_unlimited:
        remaining_usd = float("inf")
        remaining_pct = 100.0
    else:
        remaining_usd = max(total_quota_usd - daily_usage_usd, 0.0)
        if total_quota_usd <= 0:
            remaining_pct = 0.0 if daily_usage_usd > 0 else 100.0
        else:
            remaining_pct = max(min(remaining_usd / total_quota_usd * 100.0, 100.0), 0.0)
    
    # Get daily breakdown if available
    daily_breakdown = usage_data.get("daily_costs", [])
    today_breakdown = None
    if daily_breakdown:
        today_str = start_date
        for day_data in daily_breakdown:
            if day_data.get("timestamp") == today_str:
                today_breakdown = day_data
                break
    
    return {
        "success": True,
        "date": start_date,
        "token_key": mask_key(token_key),
        "total_quota_usd": total_quota_usd,
        "daily_usage_usd": daily_usage_usd,
        "daily_usage_cents": total_usage_cents,
        "remaining_usd": remaining_usd if not is_unlimited else None,
        "remaining_pct": remaining_pct,
        "unlimited": is_unlimited,
        "today_breakdown": today_breakdown,
        "raw_subscription": subscription_data,
        "raw_usage": usage_data,
    }


def format_usage_result(result: Dict[str, Any]) -> str:
    """
    Format usage result for display
    格式化使用统计结果用于显示
    """
    if not result.get("success"):
        return f"❌ Error: {result.get('error', 'Unknown error')}"
    
    token_key = result.get("token_key", "Unknown")
    date = result.get("date", "Unknown")
    daily_usage_usd = result.get("daily_usage_usd", 0)
    daily_usage_cents = result.get("daily_usage_cents", 0)
    total_quota_usd = result.get("total_quota_usd", 0)
    remaining_usd = result.get("remaining_usd")
    remaining_pct = result.get("remaining_pct", 0)
    unlimited = result.get("unlimited", False)
    
    quota_info = "Unlimited" if unlimited else f"${total_quota_usd:.2f}"
    remaining_info = "Unlimited" if unlimited else f"${remaining_usd:.4f} ({remaining_pct:.1f}%)"
    
    return f"""✅ Daily Usage Report (SK Token Method)
📅 Date: {date}
🔑 Token: {token_key}
💰 Today's Usage: ${daily_usage_usd:.4f} ({daily_usage_cents} cents)
📊 Total Quota: {quota_info}
💳 Remaining: {remaining_info}"""


def main():
    """
    Main function for testing
    主测试函数
    """
    print("🐾 SK Token Daily Usage Query Tool")
    print("=" * 60)
    
    # Configuration
    BASE_URL = input("Enter API base URL (e.g., https://api.pipidan.xyz): ").strip()
    if not BASE_URL:
        BASE_URL = "https://api.pipidan.xyz"
    
    TOKEN_KEY = input("Enter your SK token (sk-xxx): ").strip()
    if not TOKEN_KEY:
        print("❌ SK token is required!")
        return
    
    # Ensure token has sk- prefix
    if not TOKEN_KEY.startswith("sk-"):
        TOKEN_KEY = f"sk-{TOKEN_KEY}"
    
    print("\n" + "=" * 60)
    
    # Query daily usage
    result = query_daily_usage_by_sk_token(BASE_URL, TOKEN_KEY)
    
    # Display result
    print("\n📋 Result:")
    print("-" * 40)
    print(format_usage_result(result))
    
    # Also print raw JSON for debugging
    print("\n🔧 Raw JSON Response:")
    print("-" * 40)
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))


if __name__ == "__main__":
    main()
