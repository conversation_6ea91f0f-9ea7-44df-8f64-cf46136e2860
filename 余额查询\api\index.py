import json
import os
import re
from datetime import date, timedelta
from urllib.parse import parse_qs

import requests


PACKY_URL = "https://www.packycode.com/api/backend/users/info"
PIPIDAN_LEGACY_URL = "https://api-ycc.pipidan.xyz/api/v1/claude/balance"
PIPIDAN_OPENAI_BASE = os.getenv("PIPIDAN_OPENAI_BASE", "https://api.pipidan.xyz/")


def _ymd(d: date) -> str:
    return d.strftime("%Y-%m-%d")


def fetch_packy(key: str):
    try:
        r = requests.get(
            PACKY_URL,
            headers={"Authorization": f"Bearer {key}", "Content-Type": "application/json"},
            timeout=10,
        )
        if r.status_code in (400, 401, 403):
            return {"error": f"auth {r.status_code}"}
        r.raise_for_status()
        d = r.json()
        db = float(d.get("daily_budget_usd") or 0)
        ds = float(d.get("daily_spent_usd") or 0)
        mb = float(d.get("monthly_budget_usd") or 0)
        ms = float(d.get("monthly_spent_usd") or 0)
        
        # 简化显示：只返回余额，不显示可能不准确的百分比
        daily_remain = max(db - ds, 0)
        monthly_remain = max(mb - ms, 0)
        
        return {
            "daily": {"budget": db, "remain": daily_remain},
            "monthly": {"budget": mb, "remain": monthly_remain},
        }
    except Exception as e:
        return {"error": str(e)}


def fetch_pipidan_legacy(key: str):
    try:
        r = requests.get(PIPIDAN_LEGACY_URL, headers={"X-API-Key": key}, timeout=10)
        if r.status_code in (400, 401, 403):
            return {"error": f"auth {r.status_code}"}
        r.raise_for_status()
        d = r.json()
        try:
            total = d.get("total")
            remaining = d.get("remaining")
            balance = d.get("balance")
            used = d.get("used")
            remaining_usd = None
            remaining_pct = None
            if isinstance(balance, (int, float)):
                remaining_usd = float(balance)
            elif isinstance(remaining, (int, float)):
                remaining_usd = float(remaining)
            if isinstance(total, (int, float)) and total > 0 and remaining_usd is not None:
                remaining_pct = max(min(remaining_usd / float(total) * 100.0, 100.0), 0.0)
            elif isinstance(used, (int, float)) and isinstance(total, (int, float)) and total > 0:
                remaining_pct = max(min((float(total) - float(used)) / float(total) * 100.0, 100.0), 0.0)
                remaining_usd = float(total) - float(used)
            if remaining_usd is not None:
                return {"balance": round(remaining_usd, 6), "remaining_pct": (None if remaining_pct is None else round(remaining_pct, 2))}
        except Exception:
            pass

        if isinstance(d.get("daily"), dict) and isinstance(d.get("monthly"), dict):
            try:
                dd = d.get("daily")
                mm = d.get("monthly")
                def _num(x):
                    try:
                        return float(x)
                    except Exception:
                        return 0.0
                daily = {
                    "budget": _num(dd.get("budget")),
                    "spent": _num(dd.get("spent")),
                    "remain": max(_num(dd.get("budget")) - _num(dd.get("spent")), 0.0),
                    "used_pct": (0.0 if _num(dd.get("budget")) == 0 else round(_num(dd.get("spent"))/_num(dd.get("budget"))*100.0, 1))
                }
                monthly = {
                    "budget": _num(mm.get("budget")),
                    "spent": _num(mm.get("spent")),
                    "remain": max(_num(mm.get("budget")) - _num(mm.get("spent")), 0.0),
                    "used_pct": (0.0 if _num(mm.get("budget")) == 0 else round(_num(mm.get("spent"))/_num(mm.get("budget"))*100.0, 1))
                }
                return {"daily": daily, "monthly": monthly}
            except Exception:
                pass

        return {"error": "unsupported response"}
    except Exception as e:
        return {"error": str(e)}


def fetch_pipidan_openai_style(key: str, base: str = PIPIDAN_OPENAI_BASE):
    try:
        b = base.rstrip("/")
        headers = {"Authorization": f"Bearer {key}"}
        sub = requests.get(f"{b}/v1/dashboard/billing/subscription", headers=headers, timeout=15)
        if sub.status_code in (400, 401, 403):
            return {"error": f"auth {sub.status_code}"}
        sub.raise_for_status()
        subj = sub.json()
        total_usd = float(subj.get("hard_limit_usd") or 0.0)
        is_unlimited = total_usd >= 100000000
        end = date.today()
        start = end - timedelta(days=100)
        usage = requests.get(
            f"{b}/v1/dashboard/billing/usage?start_date={_ymd(start)}&end_date={_ymd(end)}",
            headers=headers,
            timeout=15,
        )
        usage.raise_for_status()
        uj = usage.json()
        used_usd = float(uj.get("total_usage") or 0.0) / 100.0
        if is_unlimited:
            return {"balance": None, "remaining_pct": 100.0}
        remaining = max(total_usd - used_usd, 0.0)
        remaining_pct = (None if total_usd <= 0 else max(min(remaining / total_usd * 100.0, 100.0), 0.0))
        return {"balance": round(remaining, 6), "remaining_pct": (None if remaining_pct is None else round(remaining_pct, 2))}
    except Exception as e:
        return {"error": str(e)}


def fetch_new_api_daily_usage(key: str, base_url: str = PIPIDAN_OPENAI_BASE):
    """
    Fetch daily usage from new-api using the log API
    通过日志API获取今日消耗，只返回安全的统计数据
    """
    try:
        import datetime

        # Get today's timestamp range
        now = datetime.datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        start_timestamp = int(today_start.timestamp())
        end_timestamp = int(now.timestamp())

        # Query logs API
        api_url = f"{base_url.rstrip('/')}/api/log/token"
        params = {"key": key}

        response = requests.get(api_url, params=params, timeout=15)
        if response.status_code != 200:
            return {"error": f"API error {response.status_code}"}

        data = response.json()
        if not data.get("success"):
            return {"error": data.get("message", "API failed")}

        logs = data.get("data", [])

        # Filter today's consumption logs and calculate safely
        today_logs = []
        for log in logs:
            log_time = log.get("created_at", 0)
            if start_timestamp <= log_time <= end_timestamp and log.get("type") == 2:
                today_logs.append(log)

        # Calculate statistics (only safe data)
        total_quota = sum(log.get("quota", 0) for log in today_logs)
        total_requests = len(today_logs)
        quota_dollars = total_quota / 100.0 if total_quota else 0

        # Return only safe statistical data
        return {
            "daily_usage_usd": quota_dollars,
            "daily_usage_cents": total_quota,
            "requests": total_requests,
            "date": now.strftime("%Y-%m-%d"),
            "provider": "new-api-daily"
        }

    except Exception as e:
        return {"error": str(e)}


def detect_and_query_single(key: str):
    sk_pat = re.compile(r"^sk-[A-Za-z0-9]{10,}$")
    cr_pat = re.compile(r"^(cr[_-])[A-Za-z0-9]+$")
    if cr_pat.match(key):
        return fetch_pipidan_legacy(key)
    if sk_pat.match(key):
        # Try new-api daily usage first
        new_api_result = fetch_new_api_daily_usage(key)
        if "error" not in new_api_result:
            return new_api_result

        # Fallback to existing methods
        first = fetch_pipidan_openai_style(key)
        if "error" in first and str(first["error"]).startswith("auth "):
            second = fetch_packy(key)
            if "error" in second and str(second["error"]).startswith("auth "):
                return fetch_pipidan_legacy(key)
            return second
        return first
    first = fetch_pipidan_openai_style(key)
    if "error" in first and str(first["error"]).startswith("auth "):
        second = fetch_packy(key)
        if "error" in second and str(second["error"]).startswith("auth "):
            return fetch_pipidan_legacy(key)
        return second
    return first


def _json(start_response, obj: dict, status: str = "200 OK"):
    body = json.dumps(obj, ensure_ascii=False).encode("utf-8")
    headers = [("Content-Type", "application/json; charset=utf-8"), ("Content-Length", str(len(body)))]
    start_response(status, headers)
    return [body]


def _html(start_response, html: str, status: str = "200 OK"):
    body = html.encode("utf-8")
    headers = [("Content-Type", "text/html; charset=utf-8"), ("Content-Length", str(len(body)))]
    start_response(status, headers)
    return [body]


def app(environ, start_response):
    method = environ.get("REQUEST_METHOD", "GET").upper()
    path = environ.get("PATH_INFO", "/")
    if path == "/":
        html = """
<!doctype html>
<html lang=zh-CN>
<head>
  <meta charset=utf-8>
  <meta name=viewport content="width=device-width, initial-scale=1">
  <title>余额查询</title>
  <style>
    :root { --bg:#0b1220; --fg:#e6edf3; --muted:#9aa7b2; --pri:#4ea1ff; --card:#121a2b; --border:#1f2a44; }
    * { box-sizing: border-box; }
    body { margin:0; background: var(--bg); color: var(--fg); font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
    .wrap { max-width: 840px; margin: 0 auto; padding: 32px 16px; }
    h1 { font-size: 22px; margin: 0 0 16px; }
    p.desc { color: var(--muted); margin: 0 0 24px; }
    .card { background: var(--card); border: 1px solid var(--border); border-radius: 12px; padding: 16px; }
    label { display:block; font-size: 13px; color: var(--muted); margin-bottom: 6px; }
    input[type=text] { width: 100%; padding: 12px 14px; border-radius: 10px; border: 1px solid var(--border); background: #0f172a; color: var(--fg); outline: none; }
    .row { display:flex; gap: 12px; align-items: center; margin-top: 12px; }
    button { padding: 10px 16px; border-radius: 10px; border: 1px solid var(--pri); background: var(--pri); color: #001e3c; cursor: pointer; font-weight: 600; }
    button:disabled { opacity: .6; cursor: not-allowed; }
    .muted { color: var(--muted); font-size: 12px; margin-top: 8px; }
    pre { margin: 16px 0 0; padding: 12px; background: #0d1526; border: 1px solid var(--border); border-radius: 10px; overflow:auto; }
    .kv { margin-top: 12px; line-height: 1.7; }
    .kv b { color: var(--fg); }
  </style>
  <script>
    async function query() {
      const ipt = document.getElementById('key');
      const btn = document.getElementById('btn');
      const out = document.getElementById('out');
      const sum = document.getElementById('sum');
      const key = (ipt.value||'').trim();
      if (!key) { alert('请先输入 Key'); ipt.focus(); return; }
      btn.disabled = true; btn.textContent = '查询中...'; out.textContent=''; sum.innerHTML='';
      try {
        const resp = await fetch('/balance?key=' + encodeURIComponent(key));
        const data = await resp.json();
        if (!data.ok) throw new Error(data.error || '查询失败');
        const r = data.result || {};
        out.textContent = JSON.stringify(r, null, 2);
        let html = '';
        if (r.error) {
          html = `<div style='color:#ff8080'><b>错误</b>：${String(r.error)}</div>`;
        } else if (r.hasOwnProperty('balance') || r.hasOwnProperty('remaining_pct')) {
          const balKnown = typeof r.balance === 'number';
          const balText = balKnown ? '$' + (r.balance||0).toFixed(2) : 'XX';
          html = `<div><b>余额</b>：${balText}</div>`;
        } else if ((r.daily && r.monthly)) {
          try {
            const d = r.daily||{}, m = r.monthly||{};
            html = `<div><b>日</b>：$${(d.remain??0).toFixed(2)} / $${(d.budget??0).toFixed(2)} · <b>月</b>：$${(m.remain??0).toFixed(2)} / $${(m.budget??0).toFixed(2)}`;
          } catch(e) {}
        } else {
          html = '<div>已返回数据，见下方</div>';
        }
        sum.innerHTML = html;
      } catch (e) {
        out.textContent = String(e);
        sum.innerHTML = `<div style='color:#ff8080'><b>错误</b>：${String(e)}</div>`;
      } finally {
        btn.disabled = false; btn.textContent = '查询';
      }
    }
    function onEnter(e) { if (e.key === 'Enter') query(); }
  </script>
</head>
<body>
  <div class=wrap>
    <h1>余额查询</h1>
    <p class=desc>支持多种 Key，自动识别，仅单 Key 查询。</p>
    <div class=card>
      <label for=key>请输入 Key（sk-... / cr_...）</label>
      <input id=key type=text placeholder="例如：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" onkeydown="onEnter(event)">
      <div class=row>
        <button id=btn onclick="query()">查询</button>
        <span class=muted>也可直接访问 <code>/balance?key=...</code></span>
      </div>
      <div id=sum class=kv></div>
      <pre id=out></pre>
    </div>
    <p class=muted style="text-align:center;margin-top:24px;">有问题加QQ：2433734846</p>
  </div>
</body>
</html>
"""
        return _html(start_response, html)

    if path == "/balance":
        if method != "GET":
            return _json(start_response, {"ok": False, "error": "method not allowed"}, status="405 Method Not Allowed")
        qs = parse_qs(environ.get("QUERY_STRING", ""))
        key = (qs.get("key", [None])[0] or "").strip()
        if not key:
            return _json(start_response, {"ok": False, "error": "no key provided"}, status="400 Bad Request")
        result = detect_and_query_single(key)
        return _json(start_response, {"ok": True, "result": result})

    return _json(start_response, {"ok": False, "error": "not found"}, status="404 Not Found")

