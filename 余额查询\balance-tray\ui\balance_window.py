import tkinter as tk
from tkinter import ttk, messagebox
import json
from typing import Dict, List, Any
from datetime import datetime


class BalanceWindow:
    """余额显示窗口"""
    
    def __init__(self, config: Dict[str, Any], root=None):
        self.config = config
        self.root = root  # 使用传入的根窗口
        self.window = None
        self.data = {}
        self.last_update = None
        
    def create_window(self):
        """创建窗口"""
        if self.window and self._is_window_valid():
            self.window.lift()
            self.window.focus_force()
            return self.window
        
        # 如果没有根窗口，创建一个（但要小心！）
        if not self.root:
            # 检查是否已有根窗口
            try:
                # 尝试创建根窗口
                if not hasattr(tk, '_default_root') or tk._default_root is None:
                    self.root = tk.Tk()
                    self.root.withdraw()  # 隐藏主窗口
                else:
                    self.root = tk._default_root
            except:
                self.root = tk.Tk()
                self.root.withdraw()
            
        # 创建顶级窗口而不是根窗口
        self.window = tk.Toplevel(self.root)
        self.window.title("💰 余额监控")
        self.window.geometry(f"{self.config.get('window_width', 250)}x{self.config.get('window_height', 350)}")
        self.window.resizable(False, False)
        
        # 设置窗口样式
        theme = self.config.get('theme', {})
        bg_color = theme.get('bg_color', '#0b1220')
        fg_color = theme.get('fg_color', '#e6edf3')
        
        self.window.configure(bg=bg_color)
        
        # 窗口关闭时隐藏而不是销毁
        self.window.protocol("WM_DELETE_WINDOW", self.hide_window)
        
        # 创建界面元素
        self._create_ui_elements()
        
        # 初始化显示
        self.update_display()
        
        # 强制更新窗口
        self.window.update_idletasks()
        self.window.update()
        
        # 确保窗口显示在前面
        self.window.lift()
        self.window.focus_force()
        
        return self.window
    
    def _create_ui_elements(self):
        """创建UI元素"""
        theme = self.config.get('theme', {})
        bg_color = theme.get('bg_color', '#0b1220')
        fg_color = theme.get('fg_color', '#e6edf3')
        
        # 创建主框架
        main_frame = tk.Frame(self.window, bg=bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="💰 余额监控",
            font=("微软雅黑", 14, "bold"),
            bg=bg_color,
            fg=fg_color
        )
        title_label.pack(pady=(0, 5))  # 减小标题下方空间
        
        # 分隔线
        separator1 = tk.Frame(main_frame, height=1, bg=theme.get('border_color', '#1f2a44'))
        separator1.pack(fill=tk.X, pady=(0, 5))  # 减小分隔线下方空间
        
        # 创建可滚动区域
        canvas = tk.Canvas(main_frame, bg=bg_color, highlightthickness=0)
        scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg=bg_color)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 用户账户区域
        users_label = tk.Label(
            self.scrollable_frame,
            text="用户账户",
            font=("微软雅黑", 10, "bold"),
            bg=bg_color,
            fg=fg_color,
            anchor="w"
        )
        users_label.pack(fill=tk.X, pady=(0, 3))  # 减小间距
        
        # 用户账户列表框架
        self.users_frame = tk.Frame(self.scrollable_frame, bg=bg_color)
        self.users_frame.pack(fill=tk.X, pady=(0, 5))  # 减小间距
        
        # 账号池区域
        pools_label = tk.Label(
            self.scrollable_frame,
            text="账号池",
            font=("微软雅黑", 10, "bold"),
            bg=bg_color,
            fg=fg_color,
            anchor="w"
        )
        pools_label.pack(fill=tk.X, pady=(0, 3))  # 减小间距
        
        # 账号池列表框架
        self.pools_frame = tk.Frame(self.scrollable_frame, bg=bg_color)
        self.pools_frame.pack(fill=tk.X, pady=(0, 5))  # 减小间距
        
        # 布局滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 分隔线
        separator2 = tk.Frame(main_frame, height=1, bg=theme.get('border_color', '#1f2a44'))
        separator2.pack(fill=tk.X, pady=(5, 10))
        
        # 固定在底部的状态和按钮区域
        bottom_frame = tk.Frame(main_frame, bg=bg_color)
        bottom_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 总计和状态
        self.total_label = tk.Label(
            bottom_frame,
            text="总计: $0.00",
            font=("微软雅黑", 11, "bold"),
            bg=bg_color,
            fg=theme.get('success_color', '#4ea1ff')
        )
        self.total_label.pack(fill=tk.X)
        
        self.status_label = tk.Label(
            bottom_frame,
            text="更新: 从未",
            font=("微软雅黑", 8),
            bg=bg_color,
            fg=theme.get('fg_color', '#9aa7b2')
        )
        self.status_label.config(text="更新: 正在加载...")  # 初始化时显示加载状态
        self.status_label.pack(fill=tk.X, pady=(3, 8))  # 减小间距
        
        # 按钮区域
        button_frame = tk.Frame(bottom_frame, bg=bg_color)
        button_frame.pack(fill=tk.X)
        
        self.refresh_btn = tk.Button(
            button_frame,
            text="🔄 刷新",
            font=("微软雅黑", 9),
            bg=theme.get('success_color', '#4ea1ff'),
            fg='white',
            border=0,
            padx=10,
            pady=5,
            cursor="hand2",
            command=self.on_refresh_click
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.close_btn = tk.Button(
            button_frame,
            text="关闭",
            font=("微软雅黑", 9),
            bg=theme.get('border_color', '#1f2a44'),
            fg=fg_color,
            border=0,
            padx=10,
            pady=5,
            cursor="hand2",
            command=self.hide_window
        )
        self.close_btn.pack(side=tk.RIGHT)
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)
        self.scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
    
    def update_data(self, data: Dict[str, Any]):
        """更新数据"""
        self.data = data
        self.last_update = datetime.now()
        # 直接在当前线程更新显示，不使用after
        if self.window and self._is_window_valid():
            try:
                self.update_display()
            except Exception as e:
                print(f"更新窗口数据失败: {e}")
    
    def hide_window(self):
        """隐藏窗口"""
        if self.window:
            self.window.withdraw()
    
    def show_window(self):
        """显示窗口"""
        if self.window and self._is_window_valid():
            self.window.deiconify()
            self.window.lift()
            self.window.focus_force()
            # 强制更新显示
            self.window.update_idletasks()
            self.window.update()
        else:
            self.create_window()
    
    def close_window(self):
        """关闭窗口"""
        if self.window:
            self.window.destroy()
            self.window = None
    
    def on_refresh_complete(self):
        """刷新完成回调"""
        if self.window and self._is_window_valid():
            try:
                self.refresh_btn.config(text="🔄 刷新", state='normal')
            except Exception as e:
                print(f"更新刷新按钮状态失败: {e}")
    
    def update_display(self):
        """更新显示内容"""
        if not self.window or not self._is_window_valid():
            return
            
        theme = self.config.get('theme', {})
        bg_color = theme.get('bg_color', '#0b1220')
        fg_color = theme.get('fg_color', '#e6edf3')
        success_color = theme.get('success_color', '#4ea1ff')
        error_color = theme.get('error_color', '#ff6b6b')
        
        # 清空现有内容
        for widget in self.users_frame.winfo_children():
            widget.destroy()
        for widget in self.pools_frame.winfo_children():
            widget.destroy()
        
        # 显示用户数据（按余额百分比排序）
        users_data = self.data.get('users', [])
        if not users_data:
            no_users_label = tk.Label(
                self.users_frame,
                text="• 暂无数据" if not self.data else "• 无用户配置",
                font=("微软雅黑", 9),
                bg=bg_color,
                fg=theme.get('fg_color', '#9aa7b2'),
                anchor="w"
            )
            no_users_label.pack(fill=tk.X)
        else:
            # 按余额百分比排序，低余额优先
            sorted_users = self._sort_items_by_balance_percentage(users_data)
            self._create_grid_layout(self.users_frame, sorted_users, bg_color, fg_color, error_color, success_color)
        
        # 显示账号池数据（按余额百分比排序）
        pools_data = self.data.get('pools', [])
        if not pools_data:
            no_pools_label = tk.Label(
                self.pools_frame,
                text="• 暂无数据" if not self.data else "• 无账号池配置",
                font=("微软雅黑", 9),
                bg=bg_color,
                fg=theme.get('fg_color', '#9aa7b2'),
                anchor="w"
            )
            no_pools_label.pack(fill=tk.X)
        else:
            # 按余额百分比排序，低余额优先
            sorted_pools = self._sort_items_by_balance_percentage(pools_data)
            self._create_grid_layout(self.pools_frame, sorted_pools, bg_color, fg_color, error_color, success_color)
        
        # 更新总计
        total = self._calculate_total()
        self.total_label.config(text=f"总计: ${total:.2f}")
        
        # 更新状态
        if self.last_update:
            time_diff = datetime.now() - self.last_update
            if time_diff.seconds < 60:
                status_text = f"更新: {time_diff.seconds}秒前"
            elif time_diff.seconds < 3600:
                status_text = f"更新: {time_diff.seconds // 60}分钟前"
            else:
                status_text = f"更新: {time_diff.seconds // 3600}小时前"
        else:
            if not self.data:
                status_text = "更新: 正在加载..."
            else:
                status_text = "更新: 刚刚完成"
        
        self.status_label.config(text=status_text)
    
    def _is_window_valid(self) -> bool:
        """检查窗口是否有效"""
        try:
            return self.window and self.window.winfo_exists()
        except tk.TclError:
            return False
    
    def _create_grid_layout(self, parent, items, bg_color, fg_color, error_color, success_color):
        """创建网格布局，每行两个卡片"""
        current_row_frame = None
        
        for i, item in enumerate(items):
            # 每两个卡片创建一行
            if i % 2 == 0:
                current_row_frame = tk.Frame(parent, bg=bg_color)
                current_row_frame.pack(fill=tk.X, pady=1)
            
            # 创建卡片容器
            card_bg_color = self._get_card_background_color(item)
            card_frame = tk.Frame(
                current_row_frame,
                bg=card_bg_color,
                relief='flat',
                bd=1
            )
            
            # 左侧卡片或右侧卡片
            if i % 2 == 0:
                card_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(2, 1), pady=1)
            else:
                card_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(1, 2), pady=1)
            
            # 绘制卡片内容
            self._create_card_content(card_frame, item, card_bg_color, fg_color, error_color, success_color)
    
    def _create_card_content(self, card_frame, item, card_bg_color, fg_color, error_color, success_color):
        """创建卡片内容，优化布局和信息显示"""
        name = item.get('name', '未知')
        
        if 'error' in item:
            # 错误状态
            name_label = tk.Label(
                card_frame,
                text=f"❌ {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=error_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=4, pady=(3, 1))
            
            error_label = tk.Label(
                card_frame,
                text=item['error'],
                font=("微软雅黑", 6),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            error_label.pack(fill=tk.X, padx=4, pady=(0, 3))
            
        elif item.get('unlimited'):
            # 无限制状态
            name_label = tk.Label(
                card_frame,
                text=f"♾️ {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=success_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=4, pady=(3, 1))
            
            status_label = tk.Label(
                card_frame,
                text="无限制额度",
                font=("微软雅黑", 6),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            status_label.pack(fill=tk.X, padx=4, pady=(0, 3))
            
        elif 'daily' in item and 'monthly' in item:
            # Packy风格显示：只显示日预算（简化）
            daily = item['daily']
            
            daily_remain = daily.get('remain', 0)
            daily_budget = daily.get('budget', 0)
            daily_pct = 0 if daily_budget == 0 else round((daily_budget - daily_remain) / daily_budget * 100, 1)
            
            name_label = tk.Label(
                card_frame,
                text=f"💰 {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=success_color if daily_remain > 1 else error_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=4, pady=(3, 1))
            
            # 创建一个框架来容纳余额和总量，使用不同颜色
            balance_frame = tk.Frame(card_frame, bg=card_bg_color)
            balance_frame.pack(fill=tk.X, padx=4, pady=1)
            
            # 余额数字（亮绿色）
            balance_text = tk.Label(
                balance_frame,
                text=f"${daily_remain:.2f}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg="#2ecc71" if daily_remain > 1 else error_color,
                anchor="w"
            )
            balance_text.pack(side=tk.LEFT)
            
            # 分隔符和总量（灰色）
            total_text = tk.Label(
                balance_frame,
                text=f" / ${daily_budget:.2f}",
                font=("微软雅黑", 6),
                bg=card_bg_color,
                fg="#95a5a6",  # 灰色
                anchor="w"
            )
            total_text.pack(side=tk.LEFT)
            
            usage_label = tk.Label(
                card_frame,
                text=f"已用: {daily_pct}%",
                font=("微软雅黑", 6),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            usage_label.pack(fill=tk.X, padx=4, pady=(0, 3))
            
        elif 'balance' in item:
            # Pipidan风格显示：总余额
            balance = item.get('balance', 0)
            if isinstance(balance, (int, float)):
                name_label = tk.Label(
                    card_frame,
                    text=f"💳 {name}",
                    font=("微软雅黑", 8, "bold"),
                    bg=card_bg_color,
                    fg=success_color if balance > 10 else error_color,
                    anchor="w"
                )
                name_label.pack(fill=tk.X, padx=4, pady=(3, 1))
                
                # 检查是否有手动配置的总额度
                manual_total = item.get('manual_total')
                if manual_total and isinstance(manual_total, (int, float)):
                    used = manual_total - balance
                    used_pct = round(used / manual_total * 100, 1) if manual_total > 0 else 0
                    
                    # 创建一个框架来容纳余额和总量，使用不同颜色
                    balance_frame = tk.Frame(card_frame, bg=card_bg_color)
                    balance_frame.pack(fill=tk.X, padx=4, pady=1)
                    
                    # 余额数字（亮绿色）
                    balance_text = tk.Label(
                        balance_frame,
                        text=f"${balance:.2f}",
                        font=("微软雅黑", 8, "bold"),
                        bg=card_bg_color,
                        fg="#2ecc71" if balance > 10 else error_color,
                        anchor="w"
                    )
                    balance_text.pack(side=tk.LEFT)
                    
                    # 分隔符和总量（灰色）
                    total_text = tk.Label(
                        balance_frame,
                        text=f" / ${manual_total:.2f}",
                        font=("微软雅黑", 6),
                        bg=card_bg_color,
                        fg="#95a5a6",  # 灰色
                        anchor="w"
                    )
                    total_text.pack(side=tk.LEFT)
                    
                    usage_label = tk.Label(
                        card_frame,
                        text=f"剩余: {100-used_pct}%",
                        font=("微软雅黑", 6),
                        bg=card_bg_color,
                        fg=fg_color,
                        anchor="w"
                    )
                    usage_label.pack(fill=tk.X, padx=4, pady=(0, 3))
                else:
                    balance_label = tk.Label(
                        card_frame,
                        text=f"${balance:.2f}",
                        font=("微软雅黑", 8, "bold"),
                        bg=card_bg_color,
                        fg="#2ecc71" if balance > 10 else error_color,  # 余额使用亮绿色
                        anchor="w"
                    )
                    balance_label.pack(fill=tk.X, padx=4, pady=(1, 3))
            else:
                name_label = tk.Label(
                    card_frame,
                    text=f"💳 {name}",
                    font=("微软雅黑", 8, "bold"),
                    bg=card_bg_color,
                    fg=fg_color,
                    anchor="w"
                )
                name_label.pack(fill=tk.X, padx=4, pady=(3, 1))
                
                balance_label = tk.Label(
                    card_frame,
                    text=f"${balance}",
                    font=("微软雅黑", 7),
                    bg=card_bg_color,
                    fg=fg_color,
                    anchor="w"
                )
                balance_label.pack(fill=tk.X, padx=4, pady=(0, 3))
        else:
            # 数据异常
            name_label = tk.Label(
                card_frame,
                text=f"⚠️ {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=error_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=4, pady=(3, 1))
            
            error_label = tk.Label(
                card_frame,
                text="数据异常",
                font=("微软雅黑", 6),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            error_label.pack(fill=tk.X, padx=4, pady=(0, 3))
        """创建项目标签，优化布局和信息显示"""
        name = item.get('name', '未知')
        
        # 创建卡片式容器（使用动态背景色）
        card_bg_color = self._get_card_background_color(item)
        card_frame = tk.Frame(
            parent, 
            bg=card_bg_color,
            relief='flat',
            bd=1
        )
        card_frame.pack(fill=tk.X, pady=2, padx=3)  # 减小间距
        
        if 'error' in item:
            # 错误状态
            name_label = tk.Label(
                card_frame,
                text=f"❌ {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=error_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=5, pady=(4, 1))
            
            error_label = tk.Label(
                card_frame,
                text=item['error'],
                font=("微软雅黑", 7),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            error_label.pack(fill=tk.X, padx=5, pady=(0, 4))
            
        elif item.get('unlimited'):
            # 无限制状态
            name_label = tk.Label(
                card_frame,
                text=f"♾️ {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=success_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=5, pady=(4, 1))
            
            status_label = tk.Label(
                card_frame,
                text="无限制额度",
                font=("微软雅黑", 7),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            status_label.pack(fill=tk.X, padx=5, pady=(0, 4))
            
        elif 'daily' in item and 'monthly' in item:
            # Packy风格显示：只显示日预算（简化）
            daily = item['daily']
            
            daily_remain = daily.get('remain', 0)
            daily_budget = daily.get('budget', 0)
            daily_pct = 0 if daily_budget == 0 else round((daily_budget - daily_remain) / daily_budget * 100, 1)
            
            name_label = tk.Label(
                card_frame,
                text=f"💰 {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=success_color if daily_remain > 1 else error_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=5, pady=(4, 1))
            
            # 创建一个框架来容纳余额和总量，使用不同颜色
            balance_frame = tk.Frame(card_frame, bg=card_bg_color)
            balance_frame.pack(fill=tk.X, padx=5, pady=1)
            
            # 余额数字（亮绿色）
            balance_text = tk.Label(
                balance_frame,
                text=f"日预算: ${daily_remain:.2f}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg="#2ecc71" if daily_remain > 1 else error_color,
                anchor="w"
            )
            balance_text.pack(side=tk.LEFT)
            
            # 分隔符和总量（灰色）
            total_text = tk.Label(
                balance_frame,
                text=f" / ${daily_budget:.2f}",
                font=("微软雅黑", 7),
                bg=card_bg_color,
                fg="#95a5a6",  # 灰色
                anchor="w"
            )
            total_text.pack(side=tk.LEFT)
            
            usage_label = tk.Label(
                card_frame,
                text=f"已用: {daily_pct}%",
                font=("微软雅黑", 7),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            usage_label.pack(fill=tk.X, padx=5, pady=(0, 4))
            
        elif 'balance' in item:
            # Pipidan风格显示：总余额
            balance = item.get('balance', 0)
            if isinstance(balance, (int, float)):
                name_label = tk.Label(
                    card_frame,
                    text=f"💳 {name}",
                    font=("微软雅黑", 8, "bold"),
                    bg=card_bg_color,
                    fg=success_color if balance > 10 else error_color,
                    anchor="w"
                )
                name_label.pack(fill=tk.X, padx=5, pady=(4, 1))
                
                # 检查是否有手动配置的总额度
                manual_total = item.get('manual_total')
                if manual_total and isinstance(manual_total, (int, float)):
                    used = manual_total - balance
                    used_pct = round(used / manual_total * 100, 1) if manual_total > 0 else 0
                    
                    # 创建一个框架来容纳余额和总量，使用不同颜色
                    balance_frame = tk.Frame(card_frame, bg=card_bg_color)
                    balance_frame.pack(fill=tk.X, padx=5, pady=1)
                    
                    # 余额数字（亮绿色）
                    balance_text = tk.Label(
                        balance_frame,
                        text=f"余额: ${balance:.2f}",
                        font=("微软雅黑", 8, "bold"),
                        bg=card_bg_color,
                        fg="#2ecc71" if balance > 10 else error_color,
                        anchor="w"
                    )
                    balance_text.pack(side=tk.LEFT)
                    
                    # 分隔符和总量（灰色）
                    total_text = tk.Label(
                        balance_frame,
                        text=f" / ${manual_total:.2f}",
                        font=("微软雅黑", 7),
                        bg=card_bg_color,
                        fg="#95a5a6",  # 灰色
                        anchor="w"
                    )
                    total_text.pack(side=tk.LEFT)
                    
                    usage_label = tk.Label(
                        card_frame,
                        text=f"剩余: {100-used_pct}%",
                        font=("微软雅黑", 7),
                        bg=card_bg_color,
                        fg=fg_color,
                        anchor="w"
                    )
                    usage_label.pack(fill=tk.X, padx=5, pady=(0, 4))
                else:
                    balance_label = tk.Label(
                        card_frame,
                        text=f"余额: ${balance:.2f}",
                        font=("微软雅黑", 8, "bold"),
                        bg=card_bg_color,
                        fg="#2ecc71" if balance > 10 else error_color,  # 余额使用亮绿色
                        anchor="w"
                    )
                    balance_label.pack(fill=tk.X, padx=5, pady=(1, 4))
            else:
                name_label = tk.Label(
                    card_frame,
                    text=f"💳 {name}",
                    font=("微软雅黑", 8, "bold"),
                    bg=card_bg_color,
                    fg=fg_color,
                    anchor="w"
                )
                name_label.pack(fill=tk.X, padx=5, pady=(4, 1))
                
                balance_label = tk.Label(
                    card_frame,
                    text=f"${balance}",
                    font=("微软雅黑", 7),
                    bg=card_bg_color,
                    fg=fg_color,
                    anchor="w"
                )
                balance_label.pack(fill=tk.X, padx=8, pady=(0, 6))
        else:
            # 数据异常
            name_label = tk.Label(
                card_frame,
                text=f"⚠️ {name}",
                font=("微软雅黑", 8, "bold"),
                bg=card_bg_color,
                fg=error_color,
                anchor="w"
            )
            name_label.pack(fill=tk.X, padx=5, pady=(4, 1))
            
            error_label = tk.Label(
                card_frame,
                text="数据异常",
                font=("微软雅黑", 7),
                bg=card_bg_color,
                fg=fg_color,
                anchor="w"
            )
            error_label.pack(fill=tk.X, padx=5, pady=(0, 4))
    
    def _sort_items_by_balance_percentage(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据余额百分比排序，低余额优先显示"""
        def get_balance_percentage(item):
            if 'error' in item or item.get('unlimited'):
                return 100  # 错误或无限制账户排在后面
            
            if 'daily' in item and 'monthly' in item:
                # Packy类型：使用日预算百分比
                daily = item['daily']
                daily_remain = daily.get('remain', 0)
                daily_budget = daily.get('budget', 0)
                if daily_budget > 0:
                    used_pct = (daily_budget - daily_remain) / daily_budget * 100
                    return used_pct
                return 100
            
            elif 'balance' in item:
                # Pipidan类型：使用手动设置的总额度计算百分比
                balance = item.get('balance', 0)
                manual_total = item.get('manual_total')
                if manual_total and isinstance(manual_total, (int, float)) and manual_total > 0:
                    used_pct = (manual_total - balance) / manual_total * 100
                    return used_pct
                return 0  # 没有总额度配置，传统显示，排在前面
            
            return 100
        
        return sorted(items, key=get_balance_percentage, reverse=True)  # 从高到低（低余额在前）
    
    def _get_card_background_color(self, item: Dict[str, Any]) -> str:
        """根据余额百分比返回卡片背景色"""
        theme = self.config.get('theme', {})
        default_card_color = theme.get('card_color', '#121a2b')
        
        if 'error' in item or item.get('unlimited'):
            return default_card_color
        
        # 计算余额使用百分比
        used_percentage = 0
        
        if 'daily' in item and 'monthly' in item:
            # Packy类型：使用日预算百分比
            daily = item['daily']
            daily_remain = daily.get('remain', 0)
            daily_budget = daily.get('budget', 0)
            if daily_budget > 0:
                used_percentage = (daily_budget - daily_remain) / daily_budget * 100
        
        elif 'balance' in item:
            # Pipidan类型：使用手动设置的总额度计算百分比
            balance = item.get('balance', 0)
            manual_total = item.get('manual_total')
            if manual_total and isinstance(manual_total, (int, float)) and manual_total > 0:
                used_percentage = (manual_total - balance) / manual_total * 100
        
        # 根据使用百分比返回颜色
        if used_percentage >= 80:  # >80%使用率（<20%余额）
            return '#3d1418'  # 深红色
        elif used_percentage >= 50:  # 50%-80%使用率（20%-50%余额）
            return '#3d2818'  # 深橙色
        else:
            return default_card_color  # 默认深蓝色
    
    def _calculate_total(self) -> float:
        """计算总余额"""
        total = 0.0
        
        # 用户余额
        for user in self.data.get('users', []):
            if 'error' in user or user.get('unlimited'):
                continue
            if 'balance' in user and isinstance(user['balance'], (int, float)):
                total += user['balance']
            elif 'daily' in user:
                total += user['daily'].get('remain', 0)
        
        # 账号池余额
        for pool in self.data.get('pools', []):
            if 'error' in pool or pool.get('unlimited'):
                continue
            if 'balance' in pool and isinstance(pool['balance'], (int, float)):
                total += pool['balance']
            elif 'daily' in pool:
                total += pool['daily'].get('remain', 0)
        
        return total
        """计算总余额"""
        total = 0.0
        
        # 用户余额
        for user in self.data.get('users', []):
            if 'error' in user or user.get('unlimited'):
                continue
            if 'balance' in user and isinstance(user['balance'], (int, float)):
                total += user['balance']
            elif 'daily' in user:
                total += user['daily'].get('remain', 0)
        
        # 账号池余额
        for pool in self.data.get('pools', []):
            if 'error' in pool or pool.get('unlimited'):
                continue
            if 'balance' in pool and isinstance(pool['balance'], (int, float)):
                total += pool['balance']
            elif 'daily' in pool:
                total += pool['daily'].get('remain', 0)
        
        return total
    
    def on_refresh_click(self):
        """刷新按钮点击事件"""
        if hasattr(self, 'on_refresh_callback') and callable(self.on_refresh_callback):
            self.refresh_btn.config(text="🔄 刷新中...", state='disabled')
            self.on_refresh_callback()
    
    def on_refresh_complete(self):
        """刷新完成回调"""
        if self.window and self.window.winfo_exists():
            try:
                self.window.after(0, lambda: self.refresh_btn.config(text="🔄 刷新", state='normal'))
            except Exception as e:
                print(f"更新刷新按钮状态失败: {e}")
    
    def close_window(self):
        """关闭窗口"""
        if self.window:
            self.window.destroy()
            self.window = None
    
    def set_refresh_callback(self, callback):
        """设置刷新回调"""
        self.on_refresh_callback = callback