#!/usr/bin/env python3
"""
New-API Daily Usage Query Module
通过 sk- 令牌查询今日消耗的模块

基于发现的API接口：
1. GET /api/log/token?key=sk-xxx - 公开接口，通过令牌获取所有日志
2. 从日志中筛选今日的消费记录并统计

作者：Claude 4.0 Sonnet 🐾
"""

import os
import requests
import json
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional


def get_today_timestamps() -> tuple[int, int]:
    """
    Get today's start (00:00:00) and current timestamp in Unix seconds
    获取今天0点和当前时间的Unix时间戳（秒）
    """
    now = datetime.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    start_timestamp = int(today_start.timestamp())
    end_timestamp = int(now.timestamp())
    
    return start_timestamp, end_timestamp


def mask_key(key: str) -> str:
    """Mask API key for display"""
    if not key:
        return ""
    return "***" if len(key) <= 8 else f"{key[:4]}...{key[-4:]}"


def query_token_logs(base_url: str, token_key: str) -> Dict[str, Any]:
    """
    Query all logs for a specific token using the public API
    通过公开API查询指定令牌的所有日志
    
    Args:
        base_url: API base URL (e.g., "https://api.pipidan.xyz")
        token_key: Token key (sk-xxx format)
    
    Returns:
        Dictionary containing logs or error info
    """
    try:
        # Build API URL - this is a public endpoint that supports CORS
        api_url = f"{base_url.rstrip('/')}/api/log/token"
        
        # Query parameters
        params = {"key": token_key}
        
        print(f"🔍 Querying logs API: {api_url}")
        print(f"🔑 Token: {mask_key(token_key)}")
        
        # Make API request - no special headers needed for this public endpoint
        response = requests.get(api_url, params=params, timeout=15)
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                logs = data.get("data", [])
                return {
                    "success": True,
                    "logs": logs,
                    "total_logs": len(logs),
                }
            else:
                return {
                    "success": False,
                    "error": data.get("message", "API returned success=false"),
                }
        else:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text[:200]}",
            }
            
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": f"Request failed: {str(e)}",
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
        }


def calculate_daily_usage(logs: List[Dict], token_name: str = "") -> Dict[str, Any]:
    """
    Calculate today's usage from logs
    从日志中计算今日使用量
    
    Args:
        logs: List of log entries
        token_name: Optional token name filter
    
    Returns:
        Dictionary containing daily usage statistics
    """
    start_timestamp, end_timestamp = get_today_timestamps()
    
    # Filter logs for today and consumption type
    today_logs = []
    for log in logs:
        # Check if log is from today
        log_time = log.get("created_at", 0)
        if start_timestamp <= log_time <= end_timestamp:
            # Check if it's a consumption log (type 2 = LogTypeConsume)
            if log.get("type") == 2:
                # Filter by token name if specified
                if not token_name or log.get("token_name", "") == token_name:
                    today_logs.append(log)
    
    # Calculate statistics
    total_quota = sum(log.get("quota", 0) for log in today_logs)
    total_requests = len(today_logs)
    total_prompt_tokens = sum(log.get("prompt_tokens", 0) for log in today_logs)
    total_completion_tokens = sum(log.get("completion_tokens", 0) for log in today_logs)
    total_tokens = total_prompt_tokens + total_completion_tokens
    
    # Convert quota from cents to dollars
    quota_dollars = total_quota / 500000.0 if total_quota else 0
    
    # Get unique models used
    models_used = list(set(log.get("model_name", "unknown") for log in today_logs if log.get("model_name")))
    
    return {
        "success": True,
        "date": datetime.now().strftime("%Y-%m-%d"),
        "token_name": token_name or "All tokens",
        "quota_cents": total_quota,
        "quota_dollars": quota_dollars,
        "requests": total_requests,
        "prompt_tokens": total_prompt_tokens,
        "completion_tokens": total_completion_tokens,
        "total_tokens": total_tokens,
        "models_used": models_used,
        "logs_count": len(today_logs),
        "time_range": {
            "start": datetime.fromtimestamp(start_timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "end": datetime.fromtimestamp(end_timestamp).strftime("%Y-%m-%d %H:%M:%S"),
        }
    }


def query_daily_usage_by_token(base_url: str, token_key: str, token_name: str = "") -> Dict[str, Any]:
    """
    Main function to query daily usage by token key
    通过令牌密钥查询今日使用量的主函数
    
    Args:
        base_url: API base URL
        token_key: Token key (sk-xxx format)
        token_name: Optional token name filter
    
    Returns:
        Dictionary containing daily usage statistics or error info
    """
    # Step 1: Get all logs for this token
    logs_result = query_token_logs(base_url, token_key)
    
    if not logs_result.get("success"):
        return logs_result
    
    # Step 2: Calculate daily usage from logs
    logs = logs_result.get("logs", [])
    print(f"📊 Processing {len(logs)} total logs...")
    
    usage_result = calculate_daily_usage(logs, token_name)
    
    # Add token info to result
    usage_result["token_key"] = mask_key(token_key)
    usage_result["total_logs_available"] = len(logs)
    
    return usage_result


def format_usage_result(result: Dict[str, Any]) -> str:
    """
    Format usage result for display
    格式化使用统计结果用于显示
    """
    if not result.get("success"):
        return f"❌ Error: {result.get('error', 'Unknown error')}"
    
    token_name = result.get("token_name", "Unknown")
    date = result.get("date", "Unknown")
    quota_dollars = result.get("quota_dollars", 0)
    quota_cents = result.get("quota_cents", 0)
    requests = result.get("requests", 0)
    total_tokens = result.get("total_tokens", 0)
    prompt_tokens = result.get("prompt_tokens", 0)
    completion_tokens = result.get("completion_tokens", 0)
    models_used = result.get("models_used", [])
    logs_count = result.get("logs_count", 0)
    total_logs = result.get("total_logs_available", 0)
    time_range = result.get("time_range", {})
    
    models_str = ", ".join(models_used) if models_used else "None"
    
    return f"""✅ Daily Usage Report
📅 Date: {date}
🏷️  Token: {token_name}
💰 Cost: ${quota_dollars:.4f} ({quota_cents} cents)
📊 Requests: {requests}
🔤 Tokens: {total_tokens:,} (Prompt: {prompt_tokens:,}, Completion: {completion_tokens:,})
🤖 Models: {models_str}
📋 Today's logs: {logs_count} / {total_logs} total
⏰ Time range: {time_range.get('start', 'N/A')} ~ {time_range.get('end', 'N/A')}"""


def fetch_new_api_daily_usage(base_url: str, token_key: str, token_name: str = "") -> Dict[str, Any]:
    """
    Wrapper function for integration with existing balance query system
    用于集成到现有余额查询系统的包装函数

    Args:
        base_url: API base URL
        token_key: Token key (sk-xxx format)
        token_name: Optional token name filter

    Returns:
        Dictionary in format compatible with existing system
    """
    result = query_daily_usage_by_token(base_url, token_key, token_name)

    if not result.get("success"):
        return {
            "key": mask_key(token_key),
            "error": result.get("error", "Unknown error"),
            "provider": "new-api-daily"
        }

    # Format for compatibility with existing balance query system
    return {
        "key": mask_key(token_key),
        "provider": "new-api-daily",
        "daily_usage_usd": result.get("quota_dollars", 0),
        "daily_usage_cents": result.get("quota_cents", 0),
        "requests": result.get("requests", 0),
        "tokens": result.get("total_tokens", 0),
        "models_used": result.get("models_used", []),
        "date": result.get("date", ""),
        "success": True
    }


def main():
    """
    Main function for testing
    主测试函数
    """
    print("🐾 New-API Daily Usage Query Tool (Correct Version)")
    print("=" * 60)
    
    # Configuration
    BASE_URL = input("Enter API base URL (e.g., https://api.pipidan.xyz): ").strip()
    if not BASE_URL:
        BASE_URL = "https://api.pipidan.xyz"
    
    TOKEN_KEY = input("Enter your token key (sk-xxx): ").strip()
    if not TOKEN_KEY:
        print("❌ Token key is required!")
        return
    
    TOKEN_NAME = input("Enter token name to filter (optional, press Enter to skip): ").strip()
    
    print("\n" + "=" * 60)
    
    # Query daily usage
    result = query_daily_usage_by_token(BASE_URL, TOKEN_KEY, TOKEN_NAME)
    
    # Display result
    print("\n📋 Result:")
    print("-" * 40)
    print(format_usage_result(result))
    
    # Also print raw JSON for debugging
    print("\n🔧 Raw JSON Response:")
    print("-" * 40)
    print(json.dumps(result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
