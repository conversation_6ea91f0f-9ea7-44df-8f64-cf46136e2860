upstream packycode_backend {
    server api.packycode.com:443;
    # 针对流式传输的关键调优
    keepalive 16;              # 减少连接池大小，避免连接状态问题
    keepalive_requests 100;    # 减少每连接的请求数，及时刷新连接
    keepalive_timeout 30s;     # 缩短空闲超时，避免僵尸连接
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name api-cc.pipidan.xyz;

    index index.php index.html index.htm default.php default.htm default.html;

    # SSL 证书
    ssl_certificate /www/sites/api-cc.pipidan.xyz/ssl/fullchain.pem;
    ssl_certificate_key /www/sites/api-cc.pipidan.xyz/ssl/privkey.pem;
    ssl_protocols TLSv1.3 TLSv1.2 TLSv1.1 TLSv1;
    ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:!aNULL:!eNULL:!EXPORT:!DSS:!DES:!RC4:!3DES:!MD5:!PSK:!KRB5:!SRP:!CAMELLIA:!SEED;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 自动跳转到 HTTPS
    if ($scheme = http) {
        return 301 https://$host$request_uri;
    }

    # 专门给 API 的路径（/v1/...）做转发 + 头注入
    location ^~ /v1/ {
        # 保持转发到你现在的上游
        proxy_pass https://packycode_backend;  
        # proxy_pass https://api.packycode.com;   # 会自动带上原始路径和 ?beta=true 查询串
        proxy_set_header Host api.packycode.com;

        # ===== 在这里添加超时和重试设置 =====
        proxy_connect_timeout 10s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;

        proxy_next_upstream error timeout http_502 http_503;
        proxy_next_upstream_tries 1;

        # 为了流式稳定（以后用 stream:true 也不容易断）
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_cache off;                    # 确保每个请求都发送给Claude
        proxy_no_cache 1;                   # 强制不缓存
        proxy_cache_bypass 1;               # 强制绕过缓存

        # 基本转发设置
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        proxy_ssl_server_name on;
        proxy_ssl_name api.packycode.com;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;

        # === 关键：注入/覆盖必须请求头（指纹头 + 版本 + beta）===
        proxy_set_header User-Agent         "claude-cli/1.0.93 (external, cli)";
        proxy_set_header X-Stainless-Package-Version         "0.52.1";
        #proxy_set_header X-Stainless-Lang   "js";
        proxy_set_header X-Stainless-OS     "Linux";
        proxy_set_header X-Stainless-Runtime "node";

        # 如果你后端自己验证固定 KEY，取消下面这一行注释并填上它；
        # 想“透传客户端的 Authorization”就保持注释状态。
        # proxy_set_header Authorization "Bearer <YOUR_BACKEND_KEY>";

        # 非常重要：API 返回的是 JSON/SSE，不要做域名替换/重定向改写
        proxy_redirect off;
    }


    # 反向代理安全配置（防原地址暴露）
    location / {
        # proxy_pass https://api.packycode.com/;
        proxy_pass https://packycode_backend/; 
        proxy_set_header Host api.packycode.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_http_version 1.1;
        proxy_ssl_server_name on;
        proxy_ssl_name api.packycode.com;

        # ===== 在这里添加超时设置 =====
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_next_upstream error timeout http_502 http_503 http_504;
        # ===== 添加结束 =====
        
        # 禁止上游返回原地址
        proxy_redirect https://api.packycode.com/ https://api-cc.pipidan.xyz/;
        proxy_redirect default;

        # 隐藏可能暴露原地址的响应头
        proxy_hide_header Location;
        proxy_hide_header Server;
        proxy_hide_header X-Powered-By;
        proxy_hide_header Set-Cookie;

        # 修改返回 Cookie 域名
        proxy_cookie_domain api.packycode.com api-cc.pipidan.xyz;

        # 替换返回内容中的原域名
        sub_filter 'api.packycode.com' 'api-cc.pipidan.xyz';
        sub_filter_once off;
        proxy_set_header Accept-Encoding ""; # 关闭压缩方便替换

        # 防止缓存
        add_header Cache-Control no-cache;
        add_header Strict-Transport-Security "max-age=31536000";
    }

    # Let’s Encrypt 证书验证目录
    location ^~ /.well-known/acme-challenge {
        allow all;
        root /usr/share/nginx/html;
    }

    # 日志
    access_log /www/sites/api-cc.pipidan.xyz/log/access.log main;
    error_log /www/sites/api-cc.pipidan.xyz/log/error.log;
}




备份：
server {
    listen 80;
    listen 443 ssl http2;
    server_name api-cc.pipidan.xyz;

    index index.php index.html index.htm default.php default.htm default.html;

    # SSL 证书
    ssl_certificate /www/sites/api-cc.pipidan.xyz/ssl/fullchain.pem;
    ssl_certificate_key /www/sites/api-cc.pipidan.xyz/ssl/privkey.pem;
    ssl_protocols TLSv1.3 TLSv1.2 TLSv1.1 TLSv1;
    ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:!aNULL:!eNULL:!EXPORT:!DSS:!DES:!RC4:!3DES:!MD5:!PSK:!KRB5:!SRP:!CAMELLIA:!SEED;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 自动跳转到 HTTPS
    if ($scheme = http) {
        return 301 https://$host$request_uri;
    }

    # 专门给 API 的路径（/v1/...）做转发 + 头注入
    location ^~ /v1/ {
        # 保持转发到你现在的上游
        proxy_pass https://api.packycode.com;   # 会自动带上原始路径和 ?beta=true 查询串
        proxy_set_header Host api.packycode.com;

        # 基本转发设置
        proxy_http_version 1.1;
        proxy_ssl_server_name on;
        proxy_ssl_name api.packycode.com;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;

        # === 关键：注入/覆盖必须请求头（指纹头 + 版本 + beta）===
        proxy_set_header User-Agent         "claude-cli/1.0.93 (external, cli)";
        proxy_set_header X-Stainless-Package-Version         "0.52.1";
        #proxy_set_header X-Stainless-Lang   "js";
        proxy_set_header X-Stainless-OS     "Linux";
        proxy_set_header X-Stainless-Runtime "node";

        # 如果你后端自己验证固定 KEY，取消下面这一行注释并填上它；
        # 想“透传客户端的 Authorization”就保持注释状态。
        # proxy_set_header Authorization "Bearer <YOUR_BACKEND_KEY>";

        # 为了流式稳定（以后用 stream:true 也不容易断）
        proxy_buffering off;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
        proxy_set_header Connection "";

        # 非常重要：API 返回的是 JSON/SSE，不要做域名替换/重定向改写
        proxy_redirect off;
    }


    # 反向代理安全配置（防原地址暴露）
    location / {
        proxy_pass https://api.packycode.com/;
        proxy_set_header Host api.packycode.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_http_version 1.1;
        proxy_ssl_server_name on;
        proxy_ssl_name api.packycode.com;

        # 禁止上游返回原地址
        proxy_redirect https://api.packycode.com/ https://api-cc.pipidan.xyz/;
        proxy_redirect default;

        # 隐藏可能暴露原地址的响应头
        proxy_hide_header Location;
        proxy_hide_header Server;
        proxy_hide_header X-Powered-By;
        proxy_hide_header Set-Cookie;

        # 修改返回 Cookie 域名
        proxy_cookie_domain api.packycode.com api-cc.pipidan.xyz;

        # 替换返回内容中的原域名
        sub_filter 'api.packycode.com' 'api-cc.pipidan.xyz';
        sub_filter_once off;
        proxy_set_header Accept-Encoding ""; # 关闭压缩方便替换

        # 防止缓存
        add_header Cache-Control no-cache;
        add_header Strict-Transport-Security "max-age=31536000";
    }

    # Let’s Encrypt 证书验证目录
    location ^~ /.well-known/acme-challenge {
        allow all;
        root /usr/share/nginx/html;
    }

    # 日志
    access_log /www/sites/api-cc.pipidan.xyz/log/access.log main;
    error_log /www/sites/api-cc.pipidan.xyz/log/error.log;
}
