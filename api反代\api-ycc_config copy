server {
    listen 80;
    listen 443 ssl http2;
    server_name api-ycc.pipidan.xyz;

    # 证书（沿用你的路径）
    ssl_certificate     /www/sites/api-ycc.pipidan.xyz/ssl/fullchain.pem;
    ssl_certificate_key /www/sites/api-ycc.pipidan.xyz/ssl/privkey.pem;
    ssl_protocols TLSv1.3 TLSv1.2;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";

    # 日志
    access_log /www/sites/api-ycc.pipidan.xyz/log/access.log main;
    error_log  /www/sites/api-ycc.pipidan.xyz/log/error.log;

    # ACME 验证
    location ^~ /.well-known/acme-challenge/ {
        allow all;
        root /usr/share/nginx/html;
    }

    # HTTP → HTTPS
    if ($scheme = http) {
        return 301 https://$host$request_uri;
    }

    # 1) 根路径：返回空白页（不展示 UI）
    location = / {
        add_header Content-Type text/html;
        return 200 "";
    }

    # 2) 规范化无斜杠前缀，减少一次 301 往返
    location = /api  { return 301 /api/; }
    location = /v1   { return 301 /v1/;  }

    # 3) 仅代理 /api/ 到上游
    location ^~ /api/ {
        proxy_http_version 1.1;
        proxy_pass https://co.yes.vg/api/;

        proxy_ssl_server_name on;
        proxy_set_header Host              co.yes.vg;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP         $remote_addr;

        # 把通杀正则换成精确改写
        proxy_redirect https://co.yes.vg/ https://api-ycc.pipidan.xyz/;
        proxy_redirect  http://co.yes.vg/  https://api-ycc.pipidan.xyz/;

        # 改写 Set-Cookie 的 Domain
        proxy_cookie_domain co.yes.vg api-ycc.pipidan.xyz;

        proxy_connect_timeout 10s;
        proxy_send_timeout    60s;
        proxy_read_timeout    60s;
    }

    # 4) 仅代理 /v1/ 到上游（如果你的接口走的是 /v1/）
    location ^~ /v1/ {
        proxy_http_version 1.1;
        proxy_pass https://co.yes.vg/v1/;
    
        proxy_ssl_server_name on;
        proxy_set_header Host              co.yes.vg;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP         $remote_addr;
    
        # 仅改写 co.yes.vg 的绝对 Location，避免误伤第三方
        proxy_redirect https://co.yes.vg/ https://api-ycc.pipidan.xyz/;
        proxy_redirect  http://co.yes.vg/  https://api-ycc.pipidan.xyz/;
    
        # 改写 Set-Cookie 的 Domain
        proxy_cookie_domain co.yes.vg api-ycc.pipidan.xyz;
    
        # —— 指纹/版本头：只对 /v1 生效 ——
        proxy_set_header User-Agent          "claude-cli/1.0.83 (external, cli)";
    
        # 流式友好（SSE）
        proxy_buffering off;
        proxy_read_timeout 600s;   # 你也可以用 300s，看上游特性
        proxy_send_timeout 600s;
        proxy_set_header Connection "";
    
        proxy_connect_timeout 10s;
    }


    # 5) 兜底：其他路径一律 404（不暴露页面）
    location / { return 404; }

    # 处理 497（HTTPS 端口收到 HTTP）
    error_page 497 https://$host$request_uri;
}
