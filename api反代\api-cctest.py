import requests
import json

url = "https://api-cc.pipidan.xyz/v1/messages?beta=true"

headers = {
    # "Accept": "application/json",
    # "Content-Type": "application/json",
    # "X-Stainless-Retry-Count": "0",
    # "X-Stainless-Timeout": "600",
    # "X-Stainless-Lang": "js",
    "X-Stainless-Package-Version": "0.52.1",
    "X-Stainless-OS": "Windows",
    # "X-Stainless-Arch": "x64",
    "X-Stainless-Runtime": "node",
    # "X-Stainless-Runtime-Version": "v22.15.0",
    # "anthropic-dangerous-direct-browser-access": "true",
    # "anthropic-version": "2023-06-01",
    # "x-app": "cli",
    "User-Agent": "claude-cli/1.0.83 (external, cli)",
    # ⚠️ 这里换成你自己的密钥
    "Authorization": "Bearer sk-K2uExoqbIzeyqRbMYHOH5IPoB3dgW2md",
    # "anthropic-beta": "fine-grained-tool-streaming-2025-05-14",
    # "x-stainless-helper-method": "stream",
    # "accept-language": "*",
    # "sec-fetch-mode": "cors",
    # "accept-encoding": "br, gzip, deflate",
    # "content-length": "617"
}

data = {
    "model": "claude-3-5-haiku-20241022",
    "max_tokens": 512,
    "messages": [
        {"role": "user", "content": "你好,请用一句话形容过微商"}
    ],
    # "system": [
    #     {
    #         "type": "text",
    #         "text": "Analyze if this message indicates a new conversation topic. If it does, extract a 2-3 word title that captures the new topic. Format your response as a JSON object with two fields: 'isNewTopic' (boolean) and 'title' (string, or null if isNewTopic is false). Only include these fields, no other text."
    #     }
    # ],
    # "temperature": 0,
    # "metadata": {
    #     "user_id": "user_8dad92d755f0882971eadb1bde51e369df60603c9aed60ebe24ec6034da6222b_account__session_1a7d81d1-2b84-473c-8c56-76653e59a8e6"
    # },
    # "stream": True
}

response = requests.post(url, headers=headers, json=data)

print("Status:", response.status_code)
try:
    print("JSON:", response.json())
except Exception:
    print("Text:", response.text)
