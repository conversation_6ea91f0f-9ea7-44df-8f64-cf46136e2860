#!/usr/bin/env python3
"""临时修复脚本"""

def fix_balance_window():
    file_path = r"E:\code\ClaudeCodeServ\余额查询\balance-tray\ui\balance_window.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到重复的开始位置（第567行附近）
    start_idx = None
    end_idx = None
    
    for i, line in enumerate(lines):
        if '"""创建项目标签，优化布局和信息显示"""' in line:
            start_idx = i
            break
    
    # 找到正确的函数开始位置
    for i in range(start_idx + 1 if start_idx else 0, len(lines)):
        if lines[i].strip().startswith('def _sort_items_by_balance_percentage') and '"""根据余额百分比排序，低余额优先显示"""' in lines[i+1]:
            end_idx = i
            break
    
    if start_idx is not None and end_idx is not None:
        print(f"找到重复代码: 行 {start_idx+1} 到 {end_idx}")
        # 删除重复代码
        new_lines = lines[:start_idx] + lines[end_idx:]
        
        # 写入修复后的文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print("文件修复完成")
    else:
        print("未找到重复代码")

if __name__ == "__main__":
    fix_balance_window()