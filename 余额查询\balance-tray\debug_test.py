#!/usr/bin/env python3
"""调试测试脚本"""

import tkinter as tk
from ui.balance_window import BalanceWindow
import json
import os

def test_balance_window():
    # 创建测试配置
    config = {
        "window_width": 480,
        "window_height": 420,
        "theme": {
            "bg_color": "#0b1220",
            "fg_color": "#e6edf3",
            "card_color": "#121a2b",
            "border_color": "#1f2a44",
            "success_color": "#4ea1ff",
            "warning_color": "#ff8c42",
            "error_color": "#ff6b6b"
        }
    }
    
    # 创建测试数据
    test_data = {
        "users": [
            {
                "name": "测试用户",
                "balance": 50.0,
                "manual_total": 60.0,
                "provider": "pipidan"
            }
        ],
        "pools": [
            {
                "name": "测试池",
                "daily": {
                    "remain": 10.0,
                    "budget": 20.0
                },
                "monthly": {
                    "remain": 100.0,
                    "budget": 200.0
                },
                "provider": "packy"
            }
        ]
    }
    
    try:
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建余额窗口
        balance_window = BalanceWindow(config, root)
        print("BalanceWindow创建成功")
        
        # 更新数据
        balance_window.update_data(test_data)
        print("数据更新成功")
        
        # 创建并显示窗口
        balance_window.create_window()
        print("窗口创建成功")
        
        # 运行GUI
        root.mainloop()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_balance_window()