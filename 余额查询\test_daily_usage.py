#!/usr/bin/env python3
"""
Test script for querying daily token usage from new-api-main project
测试脚本：查询 new-api-main 项目中令牌的今日消耗
"""

import os
import requests
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional


def get_today_timestamps() -> tuple[int, int]:
    """
    Get today's start (00:00:00) and current timestamp in Unix seconds
    获取今天0点和当前时间的Unix时间戳（秒）
    """
    now = datetime.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    start_timestamp = int(today_start.timestamp())
    end_timestamp = int(now.timestamp())
    
    return start_timestamp, end_timestamp


def query_daily_usage(base_url: str, access_token: str, user_id: str, token_name: str = "") -> Dict[str, Any]:
    """
    Query daily usage statistics for a specific token
    查询指定令牌的今日使用统计

    Args:
        base_url: API base URL (e.g., "https://api.pipidan.xyz")
        access_token: Access token (NOT Bearer format, just the token)
        user_id: User ID (string or number)
        token_name: Token name to filter (optional)

    Returns:
        Dictionary containing usage statistics or error info
    """
    try:
        # Get today's time range
        start_timestamp, end_timestamp = get_today_timestamps()

        # Build API URL
        api_url = f"{base_url.rstrip('/')}/api/log/self/stat"

        # Build query parameters
        params = {
            "type": 2,  # LogTypeConsume (消费类型)
            "start_timestamp": start_timestamp,
            "end_timestamp": end_timestamp,
        }

        # Add token_name if provided
        if token_name:
            params["token_name"] = token_name

        # Set headers - New-API specific format
        headers = {
            "Authorization": access_token,  # NOT Bearer format!
            "New-Api-User": str(user_id),   # Required user ID header
            "Content-Type": "application/json",
        }
        
        print(f"🔍 Querying API: {api_url}")
        print(f"📅 Time range: {datetime.fromtimestamp(start_timestamp)} to {datetime.fromtimestamp(end_timestamp)}")
        if token_name:
            print(f"🏷️  Token name: {token_name}")
        print(f"🔑 Access token: {access_token[:8]}...{access_token[-4:] if len(access_token) > 12 else ''}")
        print(f"👤 User ID: {user_id}")
        
        # Make API request
        response = requests.get(api_url, params=params, headers=headers, timeout=10)
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                usage_data = data.get("data", {})
                quota = usage_data.get("quota", 0)
                rpm = usage_data.get("rpm", 0)
                tpm = usage_data.get("tpm", 0)
                
                # Convert quota from cents to dollars
                quota_dollars = quota / 100.0 if quota else 0
                
                return {
                    "success": True,
                    "quota_cents": quota,
                    "quota_dollars": quota_dollars,
                    "requests": rpm,
                    "tokens": tpm,
                    "token_name": token_name or "All tokens",
                    "date": datetime.now().strftime("%Y-%m-%d"),
                }
            else:
                return {
                    "success": False,
                    "error": data.get("message", "API returned success=false"),
                }
        else:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text[:200]}",
            }
            
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": f"Request failed: {str(e)}",
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
        }


def format_usage_result(result: Dict[str, Any]) -> str:
    """
    Format usage result for display
    格式化使用统计结果用于显示
    """
    if not result.get("success"):
        return f"❌ Error: {result.get('error', 'Unknown error')}"
    
    token_name = result.get("token_name", "Unknown")
    date = result.get("date", "Unknown")
    quota_dollars = result.get("quota_dollars", 0)
    quota_cents = result.get("quota_cents", 0)
    requests = result.get("requests", 0)
    tokens = result.get("tokens", 0)
    
    return f"""✅ Daily Usage Report
📅 Date: {date}
🏷️  Token: {token_name}
💰 Cost: ${quota_dollars:.4f} ({quota_cents} cents)
📊 Requests: {requests}
🔤 Tokens: {tokens:,}"""


def main():
    """
    Main function for testing
    主测试函数
    """
    print("🐾 Testing Daily Usage API from new-api-main project")
    print("=" * 60)
    
    # Configuration - you can modify these values
    BASE_URL = input("Enter API base URL (e.g., https://api.pipidan.xyz): ").strip()
    if not BASE_URL:
        BASE_URL = "https://api.pipidan.xyz"
    
    AUTH_TOKEN = input("Enter your authorization token: ").strip()
    if not AUTH_TOKEN:
        print("❌ Authorization token is required!")
        return
    
    TOKEN_NAME = input("Enter token name to filter (optional, press Enter to skip): ").strip()
    
    print("\n" + "=" * 60)
    
    USER_ID = input("Enter your user ID: ").strip()
    if not USER_ID:
        print("❌ User ID is required!")
        return

    # Query daily usage
    result = query_daily_usage(BASE_URL, AUTH_TOKEN, USER_ID, TOKEN_NAME)
    
    # Display result
    print("\n📋 Result:")
    print("-" * 40)
    print(format_usage_result(result))
    
    # Also print raw JSON for debugging
    print("\n🔧 Raw JSON Response:")
    print("-" * 40)
    print(json.dumps(result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
