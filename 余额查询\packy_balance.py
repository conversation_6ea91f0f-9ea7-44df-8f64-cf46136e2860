import os
import re
import requests
from typing import List, Dict, Any, Tuple
from datetime import date, timedelta

PACKY_URL = "https://www.packycode.com/api/backend/users/info"
# 旧 pipidan 接口（保留以兼容）：
PIPIDAN_URL = "https://api-ycc.pipidan.xyz/api/v1/claude/balance"

# 新增的 OpenAI 风格余额查询 BaseURL（来自你的需求与前端实现）：
PIPIDAN_OPENAI_BASE = os.getenv("PIPIDAN_OPENAI_BASE", "https://api.pipidan.xyz/")

BASE_DIR = os.path.dirname(__file__)
KEY_FILE_PACKY = os.getenv("KEY_FILE_PACKY", os.path.join(BASE_DIR, "keys.sample_packy.txt"))
KEY_FILE_PIPIDAN = os.getenv("KEY_FILE_PIPIDAN", os.path.join(BASE_DIR, "keys.sample_pipi.txt"))


def mask(key: str) -> str:
    if not key:
        return ""
    return "***" if len(key) <= 8 else f"{key[:4]}...{key[-4:]}"


def pct(spent: float, budget: float) -> float:
    return 0.0 if not budget else round(spent / budget * 100, 1)


def load_keys(path: str) -> List[str]:
    try:
        if path and os.path.isfile(path):
            with open(path, encoding="utf-8") as f:
                return [line.strip() for line in f if line.strip()]
    except Exception:
        pass
    return []


def fetch_packy(key: str) -> Dict[str, Any]:
    try:
        r = requests.get(
            PACKY_URL,
            headers={
                "Authorization": f"Bearer {key}",
                "Content-Type": "application/json",
            },
            timeout=10,
        )
        if r.status_code in (400, 401, 403):
            return {"key": mask(key), "error": f"auth {r.status_code}"}
        r.raise_for_status()
        d = r.json()
        db = float(d.get("daily_budget_usd") or 0)
        ds = float(d.get("daily_spent_usd") or 0)
        mb = float(d.get("monthly_budget_usd") or 0)
        ms = float(d.get("monthly_spent_usd") or 0)
        
        # 简化显示：只返回余额，不显示可能不准确的百分比
        daily_remain = max(db - ds, 0)
        monthly_remain = max(mb - ms, 0)
        
        return {
            "key": mask(key),
            "provider": "packy",
            "daily": {
                "budget": db,
                "remain": daily_remain,
            },
            "monthly": {
                "budget": mb,
                "remain": monthly_remain,
            },
        }
    except Exception as e:
        return {"key": mask(key), "error": str(e)}


def fetch_pipidan_legacy(key: str) -> Dict[str, Any]:
    """沿用旧的 Pipidan 接口（X-API-Key）以兼容历史使用。"""
    try:
        r = requests.get(
            PIPIDAN_URL,
            headers={"X-API-Key": key},
            timeout=10,
        )
        if r.status_code in (400, 401, 403):
            return {"key": mask(key), "error": f"auth {r.status_code}"}
        r.raise_for_status()
        d = r.json()
        summary_keys = ["balance", "remaining", "used", "total", "quota", "daily", "monthly"]
        summary = {k: d.get(k) for k in summary_keys if k in d}
        return {
            "key": mask(key),
            "provider": "pipidan-legacy",
            "summary": summary or None,
            "raw": d,
        }
    except Exception as e:
        return {"key": mask(key), "error": str(e)}


def _ymd(dt: date) -> str:
    return dt.strftime("%Y-%m-%d")


def fetch_pipidan_openai_style(key: str, base: str = PIPIDAN_OPENAI_BASE) -> Dict[str, Any]:
    """
    新增的 Pipidan 查询规则：使用 OpenAI 风格账单接口，仅输出余额与剩余百分比。
    - GET {base}/v1/dashboard/billing/subscription -> hard_limit_usd（总额度）
    - GET {base}/v1/dashboard/billing/usage?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD -> total_usage（美分）
    认证：Authorization: Bearer <key>
    """
    try:
        b = base.rstrip("/")
        headers = {"Authorization": f"Bearer {key}"}

        sub = requests.get(f"{b}/v1/dashboard/billing/subscription", headers=headers, timeout=15)
        if sub.status_code in (400, 401, 403):
            return {"key": mask(key), "error": f"auth {sub.status_code}"}
        sub.raise_for_status()
        subj = sub.json()

        total_usd = float(subj.get("hard_limit_usd") or 0.0)
        is_unlimited = total_usd >= 100000000

        end = date.today()
        start = end - timedelta(days=100)
        usage = requests.get(
            f"{b}/v1/dashboard/billing/usage?start_date={_ymd(start)}&end_date={_ymd(end)}",
            headers=headers,
            timeout=15,
        )
        usage.raise_for_status()
        uj = usage.json()
        used_usd = float(uj.get("total_usage") or 0.0) / 100.0

        if is_unlimited:
            remaining_usd = float("inf")
            remaining_pct = 100.0
        else:
            remaining_usd = max(total_usd - used_usd, 0.0)
            if total_usd <= 0:
                remaining_pct = 0.0 if used_usd > 0 else 100.0
            else:
                remaining_pct = max(min(remaining_usd / total_usd * 100.0, 100.0), 0.0)

        return {
            "key": mask(key),
            "provider": "pipidan-openai",
            "balance": (None if is_unlimited else round(remaining_usd, 6)),
            "remaining_pct": round(remaining_pct, 2),
            "unlimited": is_unlimited,
        }
    except Exception as e:
        return {"key": mask(key), "error": str(e)}


if __name__ == "__main__":
    packy_keys = load_keys(KEY_FILE_PACKY)
    pipidan_keys = load_keys(KEY_FILE_PIPIDAN)

    if not packy_keys and not pipidan_keys:
        print(
            "No keys found:\n"
            f"- Packy: Please write pk_xxx in {KEY_FILE_PACKY} (one per line)\n"
            f"- Pipidan: Please write cr_axxxx in {KEY_FILE_PIPIDAN} (one per line)\n"
            "To customize paths, set KEY_FILE_PACKY / KEY_FILE_PIPIDAN environment variables"
        )
        raise SystemExit(1)

    if packy_keys:
        print("=== Packy ===")
        for res in map(fetch_packy, packy_keys):
            if "error" in res:
                print(f"{res['key']}: ERROR {res['error']}")
            else:
                d, m = res["daily"], res["monthly"]
                print(
                    f"{res['key']}: Daily ${d['remain']:.2f}/${d['budget']:.2f} | "
                    f"Monthly ${m['remain']:.2f}/${m['budget']:.2f}"
                )
    else:
        print("=== Packy === No keys, skipped")

    if pipidan_keys:
        print("\n=== Pipidan (Auto-detect) ===")

        sk_pat = re.compile(r"^sk-[A-Za-z0-9]{48}$")
        cr_pat = re.compile(r"^(cr[_-])[A-Za-z0-9]+$")

        def print_openai_res(res: Dict[str, Any]):
            if "error" in res:
                print(f"{res['key']}: ERROR {res['error']}")
                return
            if res.get("unlimited"):
                print(f"{res['key']}: Balance: Unlimited")
            else:
                print(f"{res['key']}: Balance: ${res['balance']:.2f}")

        for k in pipidan_keys:
            if sk_pat.match(k):
                # OpenAI风格
                print_openai_res(fetch_pipidan_openai_style(k))
            elif cr_pat.match(k):
                # 旧站点
                res = fetch_pipidan_legacy(k)
                if "error" in res:
                    print(f"{res['key']}: ERROR {res['error']}")
                else:
                    summary = res.get("summary")
                    print(f"{res['key']}: {summary if summary else res.get('raw')}")
            else:
                # 未知前缀：先尝试 OpenAI 风格，若鉴权错误再回退到旧站点
                first = fetch_pipidan_openai_style(k)
                if "error" in first and str(first["error"]).startswith("auth "):
                    second = fetch_pipidan_legacy(k)
                    if "error" in second:
                        print(f"{second['key']}: ERROR {second['error']}")
                    else:
                        summary = second.get("summary")
                        print(f"{second['key']}: {summary if summary else second.get('raw')}")
                else:
                    print_openai_res(first)
    else:
        print("\n=== Pipidan === No keys, skipped")
